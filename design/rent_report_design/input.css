/* Local Raleway font */
@font-face {
  font-family: "Segoe UI";
  font-style: normal;
  font-weight: normal;
  src: url("./src/assets/fonts/Segoe\ UI.ttf") format("truetype");
  font-display: swap;
}
@font-face {
  font-family: "Segoe UI";
  font-style: normal;
  font-weight: bold;
  src: url("./src/assets/fonts/Segoe\ UI\ Bold.ttf") format("truetype");
  font-display: swap;
}
@font-face {
  font-family: "Segoe UI";
  font-style: italic;
  font-weight: bold;
  src: url("./src/assets/fonts/Segoe\ UI\ Bold.ttf") format("truetype");
  font-display: swap;
}

@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;
@plugin "daisyui" {
  themes: light --default, light --prefersdark;
}
@layer base {
  :root {
    --rmred: #ea1a1a;
    --rmred-light: #fca5a5;
    --rmred-dark: #b91c1c;

    --rmgray: #323336;
    --rmgray-light: #525359;
  }

  * {
    font-family: "Segoe UI", sans-serif;
  }

  /* Base classes */
  .text-rmred {
    color: var(--rmred);
  }
  .text-rmred-light {
    color: var(--rmred-light);
  }
  .text-rmred-dark {
    color: var(--rmred-dark);
  }

  .bg-rmred {
    background-color: var(--rmred);
  }
  .bg-rmred-light {
    background-color: var(--rmred-light);
  }
  .bg-rmred-dark {
    background-color: var(--rmred-dark);
  }
  .bg-rmgray {
    background-color: var(--rmgray);
  }
  .bg-rmgray-light {
    background-color: var(--rmgray-light);
  }

  /* Hover variants */
  .hover\:bg-rmred:hover {
    background-color: var(--rmred);
  }
  .hover\:bg-rmred-light:hover {
    background-color: var(--rmred-light);
  }
  .hover\:bg-rmred-dark:hover {
    background-color: var(--rmred-dark);
  }
  .hover\:bg-rmgray-light:hover {
    background-color: var(--rmgray-light);
  }

  /* Border classes */
  .border-rmred {
    border-color: var(--rmred);
  }
  .border-rmred-light {
    border-color: var(--rmred-light);
  }
  .border-rmred-dark {
    border-color: var(--rmred-dark);
  }

  #blog-content p,
  #blog-content li {
    @apply text-slate-800 text-lg mb-4;
  }

  #blog-content h2 {
    @apply text-2xl md:text-2xl font-bold;
  }
}
