<script>
  function toggleTooltip(e) {
    tooltip = e.nextSibling.nextSibling

    tooltip.classList.remove("hidden")
  }
</script>

<div
  id="monthly-pricing"
  class="flex gap-8 w-screen max-w-[1600px] justify-center mb-24"
>
  <div class="flex flex-col lg:flex-row lg:gap-4 gap-8 mt-8 w-full px-4">
    <!-- Basic Plan -->
    <div
      class="flex flex-col mt-0 lg:mt-8 p-8 lg:p-4 xl:p-8 gap-8 border border-gray-200 rounded-2xl hover:border-rmred transition-colors duration-200 w-1/4 lg:w-1/4 w-full"
    >
      <div class="flex flex-col gap-4">
        <div class="flex flex-col gap-2">
          <h3 class="text-2xl font-bold">Free</h3>
          <p class="text-gray-600">For individuals starting out.</p>
        </div>
        <div class="flex flex-col gap-1 h-[92px]">
          <div class="flex items-baseline gap-2">
            <span class="text-4xl font-bold">$0</span>
            <span class="text-gray-600 text-2xl">/month</span>
          </div>
          <p class="text-gray-600">Free forever</p>
        </div>
        <a href="./signup.html"
          ><button
            class="px-4 py-3 w-full bg-white border-1 border-rmred hover:bg-gray-50 text-rmred rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
          >
            Try it now
          </button></a
        >
        <!-- Rest of the Basic plan content -->
        <hr class="border-gray-200 my-4" />
        <div class="flex flex-col gap-6">
          <h4 class="font-bold text-lg">Free includes:</h4>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">Track up to 3 leases</p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              Track up to 2 properties
            </p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              1 support email per month
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Investor Plan -->
    <div class="flex flex-col rounded-2xl lg:min-w-[300px]">
      <div
        class="flex h-8 bg-rmred text-white font-bold justify-center items-center rounded-tl-md rounded-tr-md"
      >
        <span>Recommended</span>
      </div>
      <div
        class="h-full flex flex-col gap-4 p-8 lg:p-4 xl:p-8 border border-rmred hover:border-rmred transition-colors duration-200 rounded-b-2xl"
      >
        <div class="flex flex-col gap-2">
          <h3 class="text-2xl font-bold">Investor</h3>
          <p class="text-gray-600">For independent investors.</p>
        </div>
        <div class="flex flex-col gap-1 h-[94px]">
          <div class="flex items-baseline gap-2">
            <span class="text-4xl text-rmred font-bold">$15.99</span>
            <span class="text-gray-600 text-2xl">/month</span>
          </div>
          <p class="text-gray-600">
            <span class="line-through">$239.88</span> → $191.88 a year
          </p>
          <p class="text-gray-600">For a single user</p>
        </div>
        <a href="./signup.html"
          ><button
            class="px-4 py-3 w-full bg-rmred hover:bg-rmred-dark text-white rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
          >
            Sign Up
          </button></a
        >
        <!-- Rest of the Basic plan content -->
        <hr class="border-gray-200 my-4" />
        <div class="flex flex-col gap-6">
          <h4 class="font-bold text-lg">Includes free, plus:</h4>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              Free 20 reports/month to Equifax
            </p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              $1 for each additional report
            </p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              Unlimited lease tracking
            </p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              Automated tenant notifications
            </p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              Prioritized customer support
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Property Manager Plan -->
    <div
      class="flex flex-col mt-0 lg:mt-8 p-8 lg:p-4 xl:p-8 gap-8 border border-gray-200 rounded-2xl hover:border-rmred transition-colors duration-200 w-1/4 lg:w-1/4 w-full"
    >
      <div class="flex flex-col gap-4">
        <div class="flex flex-col gap-2">
          <h3 class="text-2xl font-bold">Property Manager</h3>
          <p class="text-gray-600">For larger teams.</p>
        </div>
        <div class="flex flex-col gap-2 h-[92px]">
          <div class="flex flex-col gap-1 h-[72px]">
            <div class="flex items-baseline gap-2">
              <span class="text-4xl text-rmred font-bold">$23.99</span>
              <span class="text-gray-600 text-2xl">/month</span>
            </div>
            <p class="text-gray-600">
              <span class="line-through">$359.88</span> → $287.88 a year
            </p>
            <p class="text-gray-600">Up to 10 seats</p>
          </div>
        </div>
        <a href="./signup.html"
          ><button
            class="px-4 py-3 w-full bg-white border-1 border-rmred hover:bg-gray-50 text-rmred rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
          >
            Try it now
          </button></a
        >
        <!-- Rest of the Basic plan content -->
        <hr class="border-gray-200 my-4" />
        <div class="flex flex-col gap-6">
          <h4 class="font-bold text-lg">Investor, plus:</h4>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              Unlimited reports to Equifax
            </p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              Access to team management
            </p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              Multi-level team permissions
            </p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">Agent Assignment</p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-col mt-0 lg:mt-8 p-8 lg:p-4 xl:p-8 gap-8 border border-gray-200 rounded-2xl hover:border-rmred transition-colors duration-200 lg:w-1/4 w-full"
    >
      <div class="flex flex-col gap-4">
        <div class="flex flex-col gap-2">
          <h3 class="text-2xl font-bold">Enterprise</h3>
          <p class="text-gray-600">For organizations.</p>
        </div>
        <div class="flex flex-col gap-2 h-[92px]">
          <div class="flex items-baseline gap-2">
            <span class="text-gray-600 text-lg">Contact sales for pricing</span>
          </div>
        </div>
        <a href="./signup.html"
          ><button
            class="px-4 py-3 w-full bg-white border-1 border-rmred hover:bg-gray-50 text-rmred rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
          >
            Contact Us
          </button></a
        >
        <!-- Rest of the Basic plan content -->
        <hr class="border-gray-200 my-4" />
        <div class="flex flex-col gap-6">
          <h4 class="font-bold text-lg">Everything, plus:</h4>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">Customized Branding</p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">
              Customized roles and workflows
            </p>
          </div>
          <div class="flex justify-start items-center gap-4">
            <div>
              <img src="./assets/check-circle.svg" class="w-6" />
            </div>

            <p class="max-w-[200px] xl:max-w-[200px]">API Integrations</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
