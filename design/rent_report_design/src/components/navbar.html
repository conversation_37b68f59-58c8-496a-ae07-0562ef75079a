<script>
  function openDrawer() {
    const drawer = document.getElementById("mobile-nav")

    if (drawer.classList.contains("h-0")) {
      drawer.classList.add("h-screen")
      drawer.classList.remove("h-0")
    } else {
      drawer.classList.add("h-0")
      drawer.classList.remove("h-screen")
    }
  }
</script>
<!-- Desktop Nav -->
<nav
  class="flex w-full lg:justify-center justify-between bg-gray-50 px-8 py-2"
  style="box-shadow: 0 0 0 1px rgb(237, 237, 237)"
>
  <div class="flex justify-between w-full max-w-[1376px] h-v">
    <div class="flex items-center">
      <nav class="flex gap-4 items-center text-sm">
        <a href="index.html" class="mr-4">
          <img src="./assets/logo-graphic.svg" class="max-h-8" />
        </a>
        <a
          href="./providers.html"
          class="hidden lg:block hover:bg-gray-200 py-3 px-4 rounded-md font-medium"
          >Housing Providers</a
        >
        <a
          href="./renters.html"
          class="hidden lg:block hover:bg-gray-200 py-3 px-4 rounded-md font-medium"
          >Renters</a
        >
        <a
          href="./blog.html"
          class="hidden lg:block hover:bg-gray-200 py-3 px-4 rounded-md font-medium"
          >Resources</a
        >
        <a
          href="./pricing.html"
          class="hidden lg:block hover:bg-gray-200 py-3 px-4 rounded-md font-medium"
          >Pricing</a
        >
      </nav>
    </div>
    <div>
      <div class="flex gap-8 items-center text-sm h-4 py-6">
        <!-- UNHIDE THIS WHEN WE GET A CONTACT PAGE-->
        <a href="./login.html" class="hidden hover:text-rmred-dark"
          >Contact Us</a
        >
        <div class="hidden lg:block min-w-[1px] py-4 bg-gray-300 h-full"></div>
        <a
          href="./login.html"
          class="hidden lg:block text-gray-700 hover:text-rmred-dark font-bold"
          >Log In</a
        >

        <a href="./signup.html" class="hidden lg:block"
          ><button
            class="px-4 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
          >
            Get Started
          </button></a
        >
        <button
          onClick="openDrawer()"
          class="lg:hidden p-2 min-w-11 rounded-md hover:bg-gray-100"
        >
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</nav>

<!-- Mobile drawer -->
<div
  id="mobile-nav"
  class="flex flex-col absolute w-full h-0 overflow-hidden transition-all duration-400 ease-in-out lg:hidden"
>
  <div class="bg-white w-full px-8 py-2 h-full">
    <a href="./providers.html"
      ><button class="w-full bg-white min-h-16 text-left">
        Housing Providers
      </button>
    </a>
    <a href="./renters.html"
      ><button class="w-full bg-white min-h-16 text-left">Renters</button></a
    >
    <a href="./blog.html"
      ><button class="w-full bg-white min-h-16 text-left">Resources</button></a
    >
    <a href="./pricing.html"
      ><button class="w-full bg-white min-h-16 text-left">Pricing</button></a
    >
    <a href="./blog.html"
      ><button class="w-full bg-white min-h-16 text-left">Resources</button></a
    >
    <a href="./login.html"
      ><button
        class="mt-8 px-4 py-3 w-full bg-white border-1 border-rmred hover:bg-gray-50 text-rmred rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
      >
        Log in
      </button></a
    >
    <a href="./signup.html"
      ><button
        class="mt-8 w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
      >
        Sign In
      </button></a
    >
  </div>
</div>
