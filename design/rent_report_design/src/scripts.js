let currentMode = "monthly" // Track current state

function togglePricing() {
  // Toggle between monthly and annual
  currentMode = currentMode === "monthly" ? "annual" : "monthly"

  // Update button styles
  const monthlyBtn = document.getElementById("monthly-btn")
  const annualBtn = document.getElementById("annual-btn")
  const pricePlan = document.getElementById("pricing-plan")

  if (currentMode === "monthly") {
    monthlyBtn.className =
      "px-8 py-2 rounded-full bg-white text-gray-900 w-1/2 font-medium shadow-sm transition-all duration-200"
    annualBtn.className =
      "flex flex-col justify-center px-8 rounded-full w-1/2 text-gray-600 font-medium hover:text-gray-900 transition-all duration-200"

    pricePlan.setAttribute("hx-get", "./components/pricing-monthly.html")
  } else {
    monthlyBtn.className =
      "flex justify-center flex-col px-8 py-2 w-1/2 rounded-full text-gray-600 font-medium hover:text-gray-900 transition-all duration-200"
    annualBtn.className =
      "flex flex-col px-8 rounded-full bg-white w-1/2 text-gray-900 font-medium shadow-sm transition-all duration-200"

    pricePlan.setAttribute("hx-get", "./components/pricing-annual.html")
  }

  htmx.process(pricePlan)
  htmx.trigger(pricePlan, "htmx:afterSettle")
}

// Toggles visibility of compare all features table
var toggled = false

function toggleFeatures() {
  const featureComp = document.getElementById("feature-comp")
  const grid = document.getElementById("feat-comp-grid")

  if (featureComp.style.maxHeight === "0px" || !featureComp.style.maxHeight) {
    featureComp.style.maxHeight = grid.scrollHeight + "px"
  } else {
    featureComp.style.maxHeight = "0px"
  }
}

// takes in an FAQ object and toggles the content and rotates the chevron
function toggleFAQ(elem) {
  const parents = elem.parentNode
  const contentBox = parents.childNodes

  contentBox[3].classList.toggle("hidden")
  contentBox[1].getElementsByTagName("img")[0].classList.toggle("rotate-180")
}

// Copy link functionality
function setupCopyLinkButton() {
  const copyLinkBtn = document.getElementById('copy-link-btn');
  if (copyLinkBtn) {
    copyLinkBtn.addEventListener('click', async () => {
      try {
        await navigator.clipboard.writeText(window.location.href);

        // Temporarily change button text to show success
        const originalText = copyLinkBtn.innerHTML;
        copyLinkBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
          </svg>
          Copied!
        `;

        // Reset button text after 2 seconds
        setTimeout(() => {
          copyLinkBtn.innerHTML = originalText;
        }, 2000);
      } catch (err) {
        console.error('Failed to copy link:', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = window.location.href;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // Show success message
        const originalText = copyLinkBtn.innerHTML;
        copyLinkBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
          </svg>
          Copied!
        `;

        setTimeout(() => {
          copyLinkBtn.innerHTML = originalText;
        }, 2000);
      }
    });
  }
}

document.addEventListener("DOMContentLoaded", function() {
  styleTable();
  setupCopyLinkButton();
});
