<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rental Management Software — Report Rentals</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Raleway:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link href="../output.css" rel="stylesheet" />
    <link rel="icon" href="./assets/icon-32.ico" type="image/x-icon" />
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
  </head>
  <body>
    <!-- NAV BAR-->
    <div
      class="sticky top-0 z-10"
      hx-get="./components/navbar.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>

    <!-- Main content-->
    <main
      class="flex flex-col items-center min-h-screen w-full bg-white px-4 lg:px-8 pb-16"
    >
      <!-- Hero Banner -->
      <div
        class="flex flex-col w-full max-w-[1376px] gap-11 mt-16 min-h-64 rounded-2xl px-4 items-center overflow-clip"
        style="background: linear-gradient(0deg, #fef6f6 0.44%, #fff 101.4%)"
      >
        <h1 class="text-4xl lg:text-7xl text-center max-w-[938px]">
          Simplify Rental Management
        </h1>
        <div
          class="flex flex-col gap-8 max-w-[612px] justify-items-center items-center"
        >
          <p class="text-base text-center">
            Smarter rental reporting that keeps landlords organized and gives
            renters peace of mind, all in one seamless platform.
          </p>

          <a href="signup.html"
            ><button
              class="px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
            >
              Sign Up Now
            </button></a
          >
          <div class="flex gap-4 text-xs text-gray-500">
            <p>
              Get started for free • No payment needed • Investor plan starts at
              $19.99/month
            </p>
          </div>
        </div>
        <img src="./assets/hero.svg" class="w-[975px]" />
      </div>

      <!-- Benefits Section -->
      <div class="my-24 max-w-[1376px] w-full">
        <h2 class="text-5xl text-center w-full">Benefits</h2>
        <div class="flex gap-4 lg:gap-11 justify-center mt-16 font-medium">
          <button
            class="w-52 rounded-full py-3 px-6 transition-colors duration-100 cursor-pointer"
            hx-get="./components/provider-benefits.html"
            hx-target="#benefits-content"
            hx-trigger="click"
            id="provider-tab"
            onclick="setActiveTab('provider-tab')"
          >
            Providers
          </button>
          <button
            class="w-52 rounded-full py-3 px-6 transition-colors duration-100 cursor-pointer"
            hx-get="./components/renter-benefits.html"
            hx-target="#benefits-content"
            hx-trigger="click"
            id="renter-tab"
            onclick="setActiveTab('renter-tab')"
          >
            Renters
          </button>
        </div>

        <div id="benefits-content" class="mt-16">
          <!-- Content will be loaded here -->
        </div>

        <script>
          function setActiveTab(activeId) {
            document.getElementById("provider-tab").className =
              "w-52 rounded-full py-3 px-6 transition-colors duration-100 " +
              (activeId === "provider-tab"
                ? "bg-black text-white"
                : "bg-gray-200 text-black")
            document.getElementById("renter-tab").className =
              "w-52 rounded-full py-3 px-6 transition-colors duration-100 " +
              (activeId === "renter-tab"
                ? "bg-black text-white"
                : "bg-gray-200 text-black")
          }
          // Set providers as active by default
          window.addEventListener("load", () => {
            setActiveTab("provider-tab")
            document.getElementById("provider-tab").click()
          })
        </script>
      </div>

      <!-- Why choose us -->
      <div
        class="flex bg-black text-white w-full rounded-3xl justify-center items-center"
      >
        <div class="mt-16 mb-8 max-w-[1376px] w-full px-4 lg:px-8">
          <h2 class="text-3xl lg:text-5xl text-center w-full mb-16">
            Why Choose Us?
          </h2>
          <div class="grid lg:grid-cols-2 gap-4 max-w-[1376px]">
            <!-- box 1 -->
            <a href="providers.html" class="w-full">
              <div
                class="flex flex-col items-center justify-between w-full bg-rmgray hover:bg-rmgray-light transition-colors duration-100 lg:px-16 px-4 pt-8 rounded-xl group overflow-clip cursor-pointer"
              >
                <h4
                  class="font-bold text-2xl lg:text-3xl mb-4 w-full text-left"
                >
                  Automated Reporting
                </h4>
                <p class="text-gray-300 text-base lg:text-lg">
                  Streamlined reports to credit bureaus helps you get rent
                  on-time and renters build their credit scores.
                  <span class="font-bold underline text-white">Learn More</span>
                </p>
                <img
                  src="./assets/box1.png"
                  class="mt-8 w-full lg:max-w-[392px]"
                />
              </div>
            </a>
            <!-- box 2 -->
            <a href="providers.html" class="w-full">
              <div
                class="flex flex-col items-center justify-between w-full bg-rmgray hover:bg-rmgray-light transition-colors duration-100 lg:px-16 px-4 pt-8 rounded-xl group overflow-clip cursor-pointer"
              >
                <h4
                  class="font-bold text-2xl lg:text-3xl mb-4 w-full text-left"
                >
                  Easy Record Tracking
                </h4>
                <p class="text-gray-300 text-base lg:text-lg">
                  Effortlessly keep tabs on rental payments, manage leases, and
                  ensure timely collections.
                  <span class="font-bold underline text-white">Learn More</span>
                </p>
                <img
                  src="./assets/box2.png"
                  class="mt-8 w-full lg:max-w-[392px]"
                />
              </div>
            </a>
            <!-- box 3 -->
            <a href="providers.html" class="w-full">
              <div
                class="flex flex-col items-center justify-between w-full bg-rmgray hover:bg-rmgray-light transition-colors duration-100 lg:px-16 px-4 pt-8 rounded-xl group overflow-clip cursor-pointer"
              >
                <h4
                  class="font-bold text-2xl lg:text-3xl mb-4 w-full text-left"
                >
                  Enhanced Rental Screening
                </h4>
                <p class="text-gray-300 text-base lg:text-lg">
                  Make informed rental decisions from complaint histories
                  aggregated from the community.
                  <span class="font-bold underline text-white">Learn More</span>
                </p>
                <img
                  src="./assets/box3.png"
                  class="mt-8 w-full lg:max-w-[392px]"
                />
              </div>
            </a>
            <!-- box 4 -->
            <a href="providers.html" class="w-full">
              <div
                class="h-full bg-rmgray hover:bg-rmgray-light transition-colors duration-100 rounded-xl group overflow-clip cursor-pointer"
              >
                <div
                  class="flex flex-col items-center justify-between w-full lg:px-16 px-4 pt-8"
                >
                  <h4
                    class="font-bold text-2xl lg:text-3xl mb-4 w-full text-left"
                  >
                    Verified Tenant Records
                  </h4>
                  <p class="text-gray-300 text-base lg:text-lg">
                    Help tenants build a renting history that tenants can use
                    when applying for their next rental.
                    <span class="font-bold underline text-white"
                      >Learn More</span
                    >
                  </p>
                  <img
                    src="./assets/box4.png"
                    class="mt-8 w-full lg:max-w-[474px]"
                  />
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>

      <!-- Equifax Callout -->
      <div class="my-32 max-w-[1376px]">
        <h2 class="text-5xl text-center w-full">Powered By Equifax</h2>
        <div
          class="flex lg:flex-row flex-col justify-center items-center align-middle w-full mt-16 gap-16"
        >
          <img src="./assets/Equifax_Logo.svg" />
          <div class="w-full lg:w-1/2">
            <p class="mb-4">
              Our platform is proudly partnered with Equifax, one of the most
              trusted names in credit reporting. Make informed decisions while
              empowering renters to build and improve their credit history.
            </p>
            <p>
              With Equifax's trusted backing, you can be confident in the
              transparency and security of every transaction.
            </p>
          </div>
        </div>
      </div>

      <!-- end CTA -->
      <div class="flex flex-col items-center gap-8 lg:gap-16 mt-24">
        <h3 class="text-2xl lg:text-3xl font-medium text-center lg:text-left">
          Start making informed rental decisions
        </h3>
        <a href="signup.html"
          ><button
            class="px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
          >
            Get Started
          </button>
        </a>
      </div>
    </main>

    <!--Footer-->
    <div
      hx-get="./components/footer.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>
  </body>
</html>
