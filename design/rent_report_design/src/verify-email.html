<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reset Password</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Raleway:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="icon" href="./assets/icon-32.ico" type="image/x-icon" />
    <link href="../output.css" rel="stylesheet" />
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <!-- Font is already imported in CSS -->
  </head>
  <body>
    <!-- NAV BAR-->
    <div
      class="sticky top-0 z-10"
      hx-get="./components/navbar.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>

    <!-- Main content-->
    <main class="flex flex-col items-center min-h-screen w-full bg-white px-8">
      <div class="flex flex-col items-center mt-16 gap-8 w-full md:w-[552px]">
        <h2 class="font-bold text-2xl">Check your email</h2>
        <p>
          A 6 digit code has been sent to
          <span class="font-bold"><EMAIL></span>
        </p>
        <!-- Success message when email is sent -->
        <p class="hidden font-bold">
          If the account exists, a password reset email has been sent to
          <EMAIL>. Please check your spam folder if you do not see it.
        </p>
        <div class="flex flex-col gap-4 w-full">
          <label for="email" class="font-medium">Verification code</label>
          <input
            type="email"
            id="email"
            name="email"
            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
            placeholder="0 0 0 0 0 0"
          />
        </div>
        <div class="w-full">
          <button
            id="resendButton"
            class="font-bold text-rmred underline"
            onclick="startCountdown()"
          >
            Resend verification
          </button>
          <span id="countdown" class="hidden ml-2 text-gray-500"></span>
        </div>
        <button
          class="w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
        >
          Confirm
        </button>
        <a
          href="login.html"
          class="text-center text-rmred hover:text-rmred-dark font-medium underline"
        >
          Return to login
        </a>
      </div>
    </main>

    <!--Footer-->
    <div
      hx-get="./components/footer.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>

    <script>
      function startCountdown() {
        const button = document.getElementById("resendButton")
        const countdownEl = document.getElementById("countdown")
        let timeLeft = 60 // 60 seconds

        // Disable button
        button.disabled = true
        button.classList.add("opacity-50", "cursor-not-allowed")
        countdownEl.classList.remove("hidden")

        const timer = setInterval(() => {
          timeLeft--
          countdownEl.textContent = `(${timeLeft}s)`

          if (timeLeft <= 0) {
            // Re-enable button
            clearInterval(timer)
            button.disabled = false
            button.classList.remove("opacity-50", "cursor-not-allowed")
            countdownEl.classList.add("hidden")
          }
        }, 1000)
      }
    </script>
  </body>
</html>
