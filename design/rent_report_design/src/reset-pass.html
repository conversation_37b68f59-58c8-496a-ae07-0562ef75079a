<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reset Password</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Raleway:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link href="../output.css" rel="stylesheet" />
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <!-- Font is already imported in CSS -->
  </head>
  <body>
    <!-- NAV BAR-->
    <div
      class="sticky top-0 z-10"
      hx-get="./components/navbar.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>

    <!-- Main content-->
    <main class="flex flex-col items-center min-h-screen w-full bg-white px-8">
      <div class="flex flex-col items-center mt-16 gap-8 w-full md:w-[552px]">
        <h2 class="font-bold text-2xl">Reset your password</h2>
        <p>
          Enter the email address associated with your account to reset your
          password.
        </p>
        <!-- Success message when email is sent -->
        <p class="hidden font-bold">
          If the account exists, a password reset email has been sent to
          <EMAIL>. Please check your spam folder if you do not see it.
        </p>
        <div class="flex flex-col gap-4 w-full">
          <label for="email" class="font-medium">Email</label>
          <input
            type="email"
            id="email"
            name="email"
            class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
            placeholder="Enter your email"
          />
        </div>
        <a href="verify-email.html"
          ><button
            class="w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
          >
            Reset Password
          </button>
        </a>
        <a
          href="login.html"
          class="text-center text-rmred hover:text-rmred-dark font-medium underline"
        >
          Return to login
        </a>
      </div>
    </main>

    <!--Footer-->
    <div
      hx-get="./components/footer.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>
  </body>
</html>
