<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Create Account - Try Report Rentals for free</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Raleway:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link href="../output.css" rel="stylesheet" />
    <link rel="icon" href="./assets/icon-32.ico" type="image/x-icon" />
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <!-- Font is already imported in CSS -->
  </head>
  <body>
    <!-- NAV BAR-->
    <div
      class="sticky top-0 z-10"
      hx-get="./components/navbar.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>

    <!-- Main content-->
    <main class="flex flex-col items-center min-h-screen w-full bg-white px-8">
      <div class="flex flex-col items-center my-16 gap-8 w-full md:w-[552px]">
        <h2 class="font-bold text-2xl">Create an account</h2>
        <p>
          Already have an account?
          <a
            href="login.html"
            class="text-rmred hover:text-rmred-dark font-medium underline"
            >Sign in</a
          >
        </p>
        <div class="g-signin2 bg-gray-200" data-onsuccess="onSignIn">
          (placeholder) Google create account
        </div>
        <hr class="w-full border-gray-300" />
        <div class="flex flex-col gap-8 w-full">
          <div class="flex flex-col gap-4 w-full">
            <label for="email" class="font-medium">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Enter your email"
            />
          </div>
          <div class="flex flex-col gap-4 w-full">
            <label for="password" class="font-medium">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Enter your password"
            />
          </div>
          <div class="flex flex-col gap-4 w-full">
            <label for="verify-password" class="font-medium"
              >Verify Password</label
            >
            <input
              type="password"
              id="verify-password"
              name="verify-password"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Re-enter your password"
            />
          </div>
          <!-- Referral Code Section -->
          <div class="flex flex-col gap-2">
            <label for="verify-password" class="font-medium"
              >Referral Code</label
            >
            <div class="flex gap-2">
              <input
                type="text"
                id="referral"
                name="referral"
                placeholder="Enter code"
                class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              />
              <button
                class="px-4 py-2 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium transition-colors duration-200"
              >
                Apply
              </button>
            </div>
          </div>
          <div class="flex flex-col gap-4 w-full">
            <label class="font-medium">Account Type</label>
            <div class="flex gap-8">
              <div class="flex items-center gap-2">
                <input
                  type="radio"
                  id="renter"
                  name="account-type"
                  value="renter"
                  class="accent-rmred w-4 h-4 cursor-pointer"
                />
                <label for="renter" class="cursor-pointer">Renter</label>
              </div>
              <div class="flex items-center gap-2">
                <input
                  type="radio"
                  id="provider"
                  name="account-type"
                  value="provider"
                  class="accent-rmred w-4 h-4 cursor-pointer"
                />
                <label for="provider" class="cursor-pointer"
                  >Property Provider</label
                >
              </div>
            </div>
          </div>

          <div class="flex items-start gap-2">
            <input
              type="checkbox"
              id="terms"
              name="terms"
              class="w-4 h-4 accent-rmred cursor-pointer mt-1"
            />
            <label for="terms" class="text-gray-700 cursor-pointer">
              I have read and agreed to Report Rental's
              <a
                href="./tac.html"
                class="text-rmred hover:text-rmred-dark underline"
                >Terms and Conditions</a
              >
              and
              <a
                href="./privacypolicy.html"
                class="text-rmred hover:text-rmred-dark underline"
                >Privacy Policy</a
              >
            </label>
          </div>
          <button
            class="w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
          >
            Create Account
          </button>
        </div>
      </div>
    </main>

    <!--Footer-->
    <div
      hx-get="./components/footer.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>
  </body>
</html>
