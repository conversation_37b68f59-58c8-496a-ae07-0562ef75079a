<!DOCTYPE html>
<html lang="en" class="light">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Log in - Report Rentals</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Raleway:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link href="../output.css" rel="stylesheet" />
    <link rel="icon" href="./assets/icon-32.ico" type="image/x-icon" />
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <!-- Font is already imported in CSS -->
  </head>
  <body>
    <!-- NAV BAR-->
    <div
      class="sticky top-0 z-10"
      hx-get="./components/navbar.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>

    <!-- Main content-->
    <main class="flex flex-col items-center min-h-screen w-full bg-white px-8">
      <div class="flex flex-col items-center mt-16 gap-8 w-full md:w-[552px]">
        <h2 class="font-bold text-2xl">Welcome back</h2>
        <p>
          Don't have an account?
          <a
            href="signup.html"
            class="text-rmred hover:text-rmred-dark font-medium underline"
            >Create an account</a
          >
        </p>
        <div class="g-signin2 bg-gray-200" data-onsuccess="onSignIn">
          (placeholder) Google signin
        </div>
        <hr class="w-full border-gray-300" />
        <div class="flex flex-col gap-8 w-full">
          <div class="flex flex-col gap-4 w-full">
            <label for="email" class="font-medium">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Enter your email"
            />
          </div>
          <div class="flex flex-col gap-4 w-full">
            <label for="password" class="font-medium">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Enter your password"
            />
          </div>
          <div class="flex items-center gap-2">
            <input
              type="checkbox"
              id="remember"
              name="remember"
              class="w-4 h-4 accent-rmred cursor-pointer"
            />
            <label for="remember" class="text-gray-700 cursor-pointer">
              Remember me
            </label>
          </div>
          <a href="verify-email.html"
            ><button
              class="w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
            >
              Sign In
            </button>
          </a>
          <a
            href="reset-pass.html"
            class="text-center text-rmred hover:text-rmred-dark font-medium underline"
          >
            Forgot your password?
          </a>
        </div>
      </div>
    </main>

    <!--Footer-->
    <div
      hx-get="./components/footer.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>
  </body>
</html>
