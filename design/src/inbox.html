<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Inbox</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
  </head>
  <body class="min-h-screen bg-gray-200">
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>

      <main
        class="h-screen w-full bg-slate-100 overflow-auto lg:px-16 md:px-8 px-4"
      >
        <div class="max-w-[1440px] w-full mx-auto mb-32">
          <!-- Header Section -->
          <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
            <h1 class="text-2xl">Inbox</h1>

            <div class="flex gap-2 mt-4">
              <label class="input">
                <svg
                  class="h-[1em] opacity-50"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                >
                  <g
                    stroke-linejoin="round"
                    stroke-linecap="round"
                    stroke-width="2.5"
                    fill="none"
                    stroke="currentColor"
                  >
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.3-4.3"></path>
                  </g>
                </svg>
                <input
                  type="search"
                  class="grow"
                  placeholder="Search messages"
                />
              </label>
              <button
                class="hidden sm:block bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm whitespace-nowrap"
              >
                Search
              </button>
            </div>

            <div
              class="flex md:flex-row flex-col gap-4 justify-between mt-4 w-full"
            >
              <div role="tablist" class="tabs tabs-box mt-4">
                <a role="tab" class="tab tab-active">Inbox</a>
                <a role="tab" class="tab">Sent Messages</a>
              </div>

              <div class="flex items-end">
                <button
                  class="flex items-center whitespace-nowrap bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 gap-2 rounded text-sm"
                  hx-get="components/modal-send-message.html"
                  hx-target="#modal"
                  hx-swap="innerHTML"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Send Message
                </button>
              </div>
            </div>
          </div>

          <!-- Inbox Table Section -->
          <div class="max-w-[1440px] w-full mx-auto mb-32">
            <!-- Desktop View -->
            <div class="hidden lg:block">
              <table
                class="w-full text-sm rounded-md overflow-hidden mt-4"
                style="box-shadow: 0 0 0 1px rgb(203 213 225)"
              >
                <thead>
                  <tr
                    class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300"
                  >
                    <th class="p-4 w-5/12">
                      <span class="flex items-center gap-2"> Subject </span>
                    </th>
                    <th class="p-4 w-4/12">
                      <span class="flex items-center gap-2"> From </span>
                    </th>
                    <th class="p-4 w-2/12">
                      <span class="flex items-center gap-2"> Date </span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    class="bg-white hover:bg-slate-100 text-left group cursor-pointer text-gray-500"
                  >
                    <td class="p-4 text-black font-bold">
                      Welcome to the platform!
                    </td>
                    <td class="p-4 text-black font-bold">John Doe</td>
                    <td class="p-4">2024-05-01</td>
                  </tr>
                  <tr
                    class="bg-slate-50 hover:bg-slate-100 text-left group cursor-pointer border-t border-slate-300 text-gray-500"
                  >
                    <td class="p-4">Your ticket has been updated</td>
                    <td class="p-4 text-black">Support</td>
                    <td class="p-4">2024-04-30</td>
                  </tr>
                  <!-- Add more rows as needed -->
                </tbody>
              </table>
              <!-- Desktop Pagination -->
              <div
                class="hidden lg:flex justify-between items-center gap-4 px-4 py-2 mt-4 bg-white border border-gray-300 rounded-lg"
              >
                <span class="text-sm text-gray-500"
                  >Showing 2 of 2 messages</span
                >
                <div class="join">
                  <button class="join-item btn btn-active">1</button>
                  <button class="join-item btn">2</button>
                </div>
              </div>
            </div>

            <!-- Mobile/Tablet View -->
            <div class="lg:hidden">
              <div
                class="mt-4 rounded-md overflow-hidden"
                style="box-shadow: 0 0 0 1px rgb(203 213 225)"
              >
                <div class="flex flex-wrap p-4 bg-white cursor-pointer group">
                  <span class="w-full text-black font-bold"
                    >Welcome to the platform!</span
                  >
                  <span class="font-medium w-full mb-2">John Doe</span>
                  <div
                    class="flex justify-between w-full mt-2 text-sm text-gray-500"
                  >
                    <span>2024-05-01</span>
                  </div>
                </div>
                <div
                  class="flex flex-wrap p-4 bg-slate-50 cursor-pointer group border-t border-slate-300"
                >
                  <span class="w-full">Your ticket has been updated</span>
                  <span class="w-full mb-2">Support</span>
                  <div
                    class="flex justify-between w-full mt-2 text-sm text-gray-500"
                  >
                    <span>2024-04-30</span>
                  </div>
                </div>
              </div>
              <!-- Mobile Pagination -->
              <div
                class="flex justify-center items-center gap-4 px-4 py-2 mt-4 bg-white border rounded-lg"
              >
                <button class="p-2 border rounded-md">
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>
                <span class="text-sm">1 of 1</span>
                <button class="p-2 border rounded-md">
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Modal Container -->
    <div id="modal" class=""></div>
  </body>
</html>