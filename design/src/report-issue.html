<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Report a problem</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
  </head>
  <body class="min-h-screen bg-gray-200">
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>

      <main
        class="h-screen w-full bg-slate-100 overflow-auto lg:px-16 md:px-8 px-4"
      >
        <div class="max-w-[1440px] w-full mx-auto mb-32">
          <!-- Header Section -->
          <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
            <h1 class="text-2xl">Submit a report</h1>


          <!-- Report Issue Form Section -->
          <div class="max-w-[600px] w-full mx-auto mb-32 mt-8">
            <form
              class="bg-white rounded-lg shadow-md p-6 flex flex-col gap-6"
              enctype="multipart/form-data"
            >
              <!-- Subject -->
              <fieldset class="fieldset">
                <legend class="fieldset-legend">Subject</legend>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  class="input input-bordered w-full"
                  placeholder="Enter subject"
                  required
                />
              </fieldset>
              <!-- Category -->
              <fieldset class="fieldset">
                <legend class="fieldset-legend">Category</legend>
                <select
                  id="category"
                  name="category"
                  class="select w-full"
                  required
                >
                  <option value="" disabled selected>Select a category</option>
                  <option value="bug">Bug</option>
                  <option value="feature">Feature Request</option>
                  <option value="feedback">Feedback</option>
                  <option value="other">Other</option>
                </select>
              </fieldset>
              <!-- Description -->
              <fieldset class="fieldset">
                <legend class="fieldset-legend">Description</legend>
                <textarea
                  id="description"
                  name="description"
                  rows="5"
                  class="textarea w-full"
                  placeholder="Describe the issue or request..."
                  required
                ></textarea>
              </fieldset>
              <!-- Attachments -->
                <fieldset class="fieldset">
                  <legend class="fieldset-legend">Attachments</legend>
                  <input type="file" class="file-input" />
                  <label class="label">Max size 2MB</label>
                </fieldset>
              <!-- Submit Button -->
              <div class="flex justify-end">
                <button
                  type="submit"
                  class="bg-slate-800 hover:bg-slate-900 text-white px-6 py-2 rounded text-sm font-medium transition"
                >
                  Submit Report
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>

    <!-- Modal Container -->
    <div id="modal" class=""></div>
  </body>
</html>