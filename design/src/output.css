/*! tailwindcss v4.1.6 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-slate-50: oklch(98.4% 0.003 247.858);
    --color-slate-100: oklch(96.8% 0.007 247.896);
    --color-slate-200: oklch(92.9% 0.013 255.508);
    --color-slate-300: oklch(86.9% 0.022 252.894);
    --color-slate-400: oklch(70.4% 0.04 256.788);
    --color-slate-800: oklch(27.9% 0.041 260.031);
    --color-slate-900: oklch(20.8% 0.042 265.755);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-lg: 32rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-relaxed: 1.625;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .modal {
    pointer-events: none;
    visibility: hidden;
    position: fixed;
    inset: calc(0.25rem * 0);
    margin: calc(0.25rem * 0);
    display: grid;
    height: 100%;
    max-height: none;
    width: 100%;
    max-width: none;
    align-items: center;
    justify-items: center;
    background-color: transparent;
    padding: calc(0.25rem * 0);
    color: inherit;
    overflow-x: hidden;
    transition: translate 0.3s ease-out, visibility 0.3s allow-discrete, background-color 0.3s ease-out, opacity 0.1s ease-out;
    overflow-y: hidden;
    overscroll-behavior: contain;
    z-index: 999;
    &::backdrop {
      display: none;
    }
    &.modal-open, &[open], &:target {
      pointer-events: auto;
      visibility: visible;
      opacity: 100%;
      background-color: oklch(0% 0 0/ 0.4);
      .modal-box {
        translate: 0 0;
        scale: 1;
        opacity: 1;
      }
    }
    @starting-style {
      &.modal-open, &[open], &:target {
        visibility: hidden;
        opacity: 0%;
      }
    }
  }
  .drawer-side {
    pointer-events: none;
    visibility: hidden;
    position: fixed;
    inset-inline-start: calc(0.25rem * 0);
    top: calc(0.25rem * 0);
    z-index: 1;
    grid-column-start: 1;
    grid-row-start: 1;
    display: grid;
    width: 100%;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    grid-template-rows: repeat(1, minmax(0, 1fr));
    align-items: flex-start;
    justify-items: start;
    overflow-x: hidden;
    overflow-y: hidden;
    overscroll-behavior: contain;
    opacity: 0%;
    transition: opacity 0.2s ease-out 0.1s allow-discrete, visibility 0.3s ease-out 0.1s allow-discrete;
    height: 100vh;
    height: 100dvh;
    > .drawer-overlay {
      position: sticky;
      top: calc(0.25rem * 0);
      cursor: pointer;
      place-self: stretch;
      background-color: oklch(0% 0 0 / 40%);
    }
    > * {
      grid-column-start: 1;
      grid-row-start: 1;
    }
    > *:not(.drawer-overlay) {
      will-change: transform;
      transition: translate 0.3s ease-out;
      translate: -100%;
      [dir="rtl"] & {
        translate: 100%;
      }
    }
  }
  .drawer-open {
    > .drawer-side {
      overflow-y: auto;
    }
    > .drawer-toggle {
      display: none;
      & ~ .drawer-side {
        pointer-events: auto;
        visibility: visible;
        position: sticky;
        display: block;
        width: auto;
        overscroll-behavior: auto;
        opacity: 100%;
        & > .drawer-overlay {
          cursor: default;
          background-color: transparent;
        }
        & > *:not(.drawer-overlay) {
          translate: 0%;
          [dir="rtl"] & {
            translate: 0%;
          }
        }
      }
      &:checked ~ .drawer-side {
        pointer-events: auto;
        visibility: visible;
      }
    }
  }
  .drawer-toggle {
    position: fixed;
    height: calc(0.25rem * 0);
    width: calc(0.25rem * 0);
    appearance: none;
    opacity: 0%;
    &:checked {
      & ~ .drawer-side {
        pointer-events: auto;
        visibility: visible;
        overflow-y: auto;
        opacity: 100%;
        & > *:not(.drawer-overlay) {
          translate: 0%;
        }
      }
    }
    &:focus-visible ~ .drawer-content label.drawer-button {
      outline: 2px solid;
      outline-offset: 2px;
    }
  }
  .tooltip {
    position: relative;
    display: inline-block;
    --tt-bg: var(--color-neutral);
    --tt-off: calc(100% + 0.5rem);
    --tt-tail: calc(100% + 1px + 0.25rem);
    > :where(.tooltip-content), &:where([data-tip]):before {
      position: absolute;
      max-width: 20rem;
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 2);
      padding-block: calc(0.25rem * 1);
      text-align: center;
      white-space: normal;
      color: var(--color-neutral-content);
      opacity: 0%;
      font-size: 0.875rem;
      line-height: 1.25;
      transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms;
      background-color: var(--tt-bg);
      width: max-content;
      pointer-events: none;
      z-index: 1;
      --tw-content: attr(data-tip);
      content: var(--tw-content);
    }
    &:after {
      position: absolute;
      position: absolute;
      opacity: 0%;
      background-color: var(--tt-bg);
      transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms;
      content: "";
      pointer-events: none;
      width: 0.625rem;
      height: 0.25rem;
      display: block;
      mask-repeat: no-repeat;
      mask-position: -1px 0;
      --mask-tooltip: url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");
      mask-image: var(--mask-tooltip);
    }
    &.tooltip-open, &[data-tip]:not([data-tip=""]):hover, &:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover, &:has(:focus-visible) {
      > .tooltip-content, &[data-tip]:before, &:after {
        opacity: 100%;
        --tt-pos: 0rem;
        transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      }
    }
    > .tooltip-content, &[data-tip]:before {
      transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));
      inset: auto auto var(--tt-off) 50%;
    }
    &:after {
      transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));
      inset: auto auto var(--tt-tail) 50%;
    }
  }
  .tab {
    position: relative;
    display: inline-flex;
    cursor: pointer;
    appearance: none;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    text-align: center;
    webkit-user-select: none;
    user-select: none;
    &:hover {
      @media (hover: hover) {
        color: var(--color-base-content);
      }
    }
    --tab-p: 1rem;
    --tab-bg: var(--color-base-100);
    --tab-border-color: var(--color-base-300);
    --tab-radius-ss: 0;
    --tab-radius-se: 0;
    --tab-radius-es: 0;
    --tab-radius-ee: 0;
    --tab-order: 0;
    --tab-radius-min: calc(0.75rem - var(--border));
    border-color: #0000;
    order: var(--tab-order);
    height: calc(var(--size-field, 0.25rem) * 10);
    font-size: 0.875rem;
    padding-inline-start: var(--tab-p);
    padding-inline-end: var(--tab-p);
    &:is(input[type="radio"]) {
      min-width: fit-content;
      &:after {
        content: attr(aria-label);
      }
    }
    &:is(label) {
      position: relative;
      input {
        position: absolute;
        inset: calc(0.25rem * 0);
        cursor: pointer;
        appearance: none;
        opacity: 0%;
      }
    }
    &:checked, &:is(label:has(:checked)), &:is(.tab-active, [aria-selected="true"]) {
      & + .tab-content {
        display: block;
        height: 100%;
      }
    }
    &:not(:checked, label:has(:checked), :hover, .tab-active, [aria-selected="true"]) {
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 50%, transparent);
      }
    }
    &:not(input):empty {
      flex-grow: 1;
      cursor: default;
    }
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:focus-visible, &:is(label:has(:checked:focus-visible)) {
      outline: 2px solid currentColor;
      outline-offset: -5px;
    }
    &[disabled] {
      pointer-events: none;
      opacity: 40%;
    }
  }
  .menu {
    display: flex;
    width: fit-content;
    flex-direction: column;
    flex-wrap: wrap;
    padding: calc(0.25rem * 2);
    --menu-active-fg: var(--color-neutral-content);
    --menu-active-bg: var(--color-neutral);
    font-size: 0.875rem;
    :where(li ul) {
      position: relative;
      margin-inline-start: calc(0.25rem * 4);
      padding-inline-start: calc(0.25rem * 2);
      white-space: nowrap;
      &:before {
        position: absolute;
        inset-inline-start: calc(0.25rem * 0);
        top: calc(0.25rem * 3);
        bottom: calc(0.25rem * 3);
        background-color: var(--color-base-content);
        opacity: 10%;
        width: var(--border);
        content: "";
      }
    }
    :where(li > .menu-dropdown:not(.menu-dropdown-show)) {
      display: none;
    }
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      display: grid;
      grid-auto-flow: column;
      align-content: flex-start;
      align-items: center;
      gap: calc(0.25rem * 2);
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 1.5);
      text-align: start;
      transition-property: color, background-color, box-shadow;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
      grid-auto-columns: minmax(auto, max-content) auto max-content;
      text-wrap: balance;
      user-select: none;
    }
    :where(li > details > summary) {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      &::-webkit-details-marker {
        display: none;
      }
    }
    :where(li > details > summary), :where(li > .menu-dropdown-toggle) {
      &:after {
        justify-self: flex-end;
        display: block;
        height: 0.375rem;
        width: 0.375rem;
        rotate: -135deg;
        translate: 0 -1px;
        transition-property: rotate, translate;
        transition-duration: 0.2s;
        content: "";
        transform-origin: 50% 50%;
        box-shadow: 2px 2px inset;
        pointer-events: none;
      }
    }
    :where(li > details[open] > summary):after, :where(li > .menu-dropdown-toggle.menu-dropdown-show):after {
      rotate: 45deg;
      translate: 0 1px;
    }
    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title), li:not(.menu-title, .disabled) > details > summary:not(.menu-title) ):not(.menu-active, :active, .btn) {
      &.menu-focus, &:focus-visible {
        cursor: pointer;
        background-color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        }
        color: var(--color-base-content);
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title):not(.menu-active, :active, .btn):hover, li:not(.menu-title, .disabled) > details > summary:not(.menu-title):not(.menu-active, :active, .btn):hover ) {
      cursor: pointer;
      background-color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
      }
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      box-shadow: 0 1px oklch(0% 0 0 / 0.01) inset, 0 -1px oklch(100% 0 0 / 0.01) inset;
    }
    :where(li:empty) {
      background-color: var(--color-base-content);
      opacity: 10%;
      margin: 0.5rem 1rem;
      height: 1px;
    }
    :where(li) {
      position: relative;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      flex-wrap: wrap;
      align-items: stretch;
      .badge {
        justify-self: flex-end;
      }
      & > *:not(ul, .menu-title, details, .btn):active, & > *:not(ul, .menu-title, details, .btn).menu-active, & > details > summary:active {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
        color: var(--menu-active-fg);
        background-color: var(--menu-active-bg);
        background-size: auto, calc(var(--noise) * 100%);
        background-image: none, var(--fx-noise);
        &:not(&:active) {
          box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
        }
      }
      &.menu-disabled {
        pointer-events: none;
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
    }
    .dropdown:focus-within {
      .menu-dropdown-toggle:after {
        rotate: 45deg;
        translate: 0 1px;
      }
    }
    .dropdown-content {
      margin-top: calc(0.25rem * 2);
      padding: calc(0.25rem * 2);
      &:before {
        display: none;
      }
    }
  }
  .dropdown {
    position: relative;
    display: inline-block;
    position-area: var(--anchor-v, bottom) var(--anchor-h, span-right);
    & > *:not(summary):focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    .dropdown-content {
      position: absolute;
    }
    &:not(details, .dropdown-open, .dropdown-hover:hover, :focus-within) {
      .dropdown-content {
        display: none;
        transform-origin: top;
        opacity: 0%;
        scale: 95%;
      }
    }
    &[popover], .dropdown-content {
      z-index: 999;
      animation: dropdown 0.2s;
      transition-property: opacity, scale, display;
      transition-behavior: allow-discrete;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
    @starting-style {
      &[popover], .dropdown-content {
        scale: 95%;
        opacity: 0;
      }
    }
    &.dropdown-open, &:not(.dropdown-hover):focus, &:focus-within {
      > [tabindex]:first-child {
        pointer-events: none;
      }
      .dropdown-content {
        opacity: 100%;
      }
    }
    &.dropdown-hover:hover {
      .dropdown-content {
        opacity: 100%;
        scale: 100%;
      }
    }
    &:is(details) {
      summary {
        &::-webkit-details-marker {
          display: none;
        }
      }
    }
    &.dropdown-open, &:focus, &:focus-within {
      .dropdown-content {
        scale: 100%;
      }
    }
    &:where([popover]) {
      background: #0000;
    }
    &[popover] {
      position: fixed;
      color: inherit;
      @supports not (position-area: bottom) {
        margin: auto;
        &.dropdown-open:not(:popover-open) {
          display: none;
          transform-origin: top;
          opacity: 0%;
          scale: 95%;
        }
        &::backdrop {
          background-color: color-mix(in oklab, #000 30%, #0000);
        }
      }
      &:not(.dropdown-open, :popover-open) {
        display: none;
        transform-origin: top;
        opacity: 0%;
        scale: 95%;
      }
    }
  }
  .btn {
    :where(&) {
      width: unset;
    }
    display: inline-flex;
    flex-shrink: 0;
    cursor: pointer;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 1.5);
    text-align: center;
    vertical-align: middle;
    outline-offset: 2px;
    webkit-user-select: none;
    user-select: none;
    padding-inline: var(--btn-p);
    color: var(--btn-fg);
    --tw-prose-links: var(--btn-fg);
    height: var(--size);
    font-size: var(--fontsize, 0.875rem);
    font-weight: 600;
    outline-color: var(--btn-color, var(--color-base-content));
    transition-property: color, background-color, border-color, box-shadow;
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    transition-duration: 0.2s;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-color: var(--btn-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--btn-noise);
    border-width: var(--border);
    border-style: solid;
    border-color: var(--btn-border);
    text-shadow: 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15));
    touch-action: manipulation;
    box-shadow: 0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);
    --size: calc(var(--size-field, 0.25rem) * 10);
    --btn-bg: var(--btn-color, var(--color-base-200));
    --btn-fg: var(--color-base-content);
    --btn-p: 1rem;
    --btn-border: var(--btn-bg);
    @supports (color: color-mix(in lab, red, red)) {
      --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));
    }
    --btn-shadow: 0 3px 2px -2px var(--btn-bg),
    0 4px 3px -2px var(--btn-bg);
    @supports (color: color-mix(in lab, red, red)) {
      --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000),
    0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);
    }
    --btn-noise: var(--fx-noise);
    .prose & {
      text-decoration-line: none;
    }
    @media (hover: hover) {
      &:hover {
        --btn-bg: var(--btn-color, var(--color-base-200));
        @supports (color: color-mix(in lab, red, red)) {
          --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
        }
      }
    }
    &:focus-visible {
      outline-width: 2px;
      outline-style: solid;
      isolation: isolate;
    }
    &:active:not(.btn-active) {
      translate: 0 0.5px;
      --btn-bg: var(--btn-color, var(--color-base-200));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);
      }
      --btn-border: var(--btn-color, var(--color-base-200));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
      }
      --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    }
    &:is(:disabled, [disabled], .btn-disabled) {
      &:not(.btn-link, .btn-ghost) {
        background-color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        }
        box-shadow: none;
      }
      pointer-events: none;
      --btn-border: #0000;
      --btn-noise: none;
      --btn-fg: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      }
      @media (hover: hover) {
        &:hover {
          pointer-events: none;
          background-color: var(--color-neutral);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
          }
          --btn-border: #0000;
          --btn-fg: var(--color-base-content);
          @supports (color: color-mix(in lab, red, red)) {
            --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
          }
        }
      }
    }
    &:is(input[type="checkbox"], input[type="radio"]) {
      appearance: none;
      &::after {
        content: attr(aria-label);
      }
    }
    &:where(input:checked:not(.filter .btn)) {
      --btn-color: var(--color-primary);
      --btn-fg: var(--color-primary-content);
      isolation: isolate;
    }
  }
  .pointer-events-auto {
    pointer-events: auto;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .invisible {
    visibility: hidden;
  }
  .list {
    display: flex;
    flex-direction: column;
    font-size: 0.875rem;
    :where(.list-row) {
      --list-grid-cols: minmax(0, auto) 1fr;
      position: relative;
      display: grid;
      grid-auto-flow: column;
      gap: calc(0.25rem * 4);
      border-radius: var(--radius-box);
      padding: calc(0.25rem * 4);
      word-break: break-word;
      grid-template-columns: var(--list-grid-cols);
      &:has(.list-col-grow:nth-child(1)) {
        --list-grid-cols: 1fr;
      }
      &:has(.list-col-grow:nth-child(2)) {
        --list-grid-cols: minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(3)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(4)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(5)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(6)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto)
        minmax(0, auto) 1fr;
      }
      :not(.list-col-wrap) {
        grid-row-start: 1;
      }
    }
    & > :not(:last-child) {
      &.list-row, .list-row {
        &:after {
          content: "";
          border-bottom: var(--border) solid;
          inset-inline: var(--radius-box);
          position: absolute;
          bottom: calc(0.25rem * 0);
          border-color: var(--color-base-content);
          @supports (color: color-mix(in lab, red, red)) {
            border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);
          }
        }
      }
    }
  }
  .toast {
    position: fixed;
    inset-inline-start: auto;
    inset-inline-end: calc(0.25rem * 4);
    top: auto;
    bottom: calc(0.25rem * 4);
    display: flex;
    flex-direction: column;
    gap: calc(0.25rem * 2);
    background-color: transparent;
    translate: var(--toast-x, 0) var(--toast-y, 0);
    width: max-content;
    max-width: calc(100vw - 2rem);
    & > * {
      animation: toast 0.25s ease-out;
    }
    &:where(.toast-start) {
      inset-inline-start: calc(0.25rem * 4);
      inset-inline-end: auto;
      --toast-x: 0;
    }
    &:where(.toast-center) {
      inset-inline-start: calc(1/2 * 100%);
      inset-inline-end: calc(1/2 * 100%);
      --toast-x: -50%;
    }
    &:where(.toast-end) {
      inset-inline-start: auto;
      inset-inline-end: calc(0.25rem * 4);
      --toast-x: 0;
    }
    &:where(.toast-bottom) {
      top: auto;
      bottom: calc(0.25rem * 4);
      --toast-y: 0;
    }
    &:where(.toast-middle) {
      top: calc(1/2 * 100%);
      bottom: auto;
      --toast-y: -50%;
    }
    &:where(.toast-top) {
      top: calc(0.25rem * 4);
      bottom: auto;
      --toast-y: 0;
    }
  }
  .toggle {
    border: var(--border) solid currentColor;
    color: var(--input-color);
    position: relative;
    display: inline-grid;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    place-content: center;
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    grid-template-columns: 0fr 1fr 1fr;
    --radius-selector-max: calc(
    var(--radius-selector) + var(--radius-selector) + var(--radius-selector)
  );
    border-radius: calc( var(--radius-selector) + min(var(--toggle-p), var(--radius-selector-max)) + min(var(--border), var(--radius-selector-max)) );
    padding: var(--toggle-p);
    box-shadow: 0 1px currentColor inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000) inset;
    }
    transition: color 0.3s, grid-template-columns 0.2s;
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 50%, #0000);
    }
    --toggle-p: calc(var(--size) * 0.125);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: calc((var(--size) * 2) - (var(--border) + var(--toggle-p)) * 2);
    height: var(--size);
    > * {
      z-index: 1;
      grid-column: span 1 / span 1;
      grid-column-start: 2;
      grid-row-start: 1;
      height: 100%;
      cursor: pointer;
      appearance: none;
      background-color: transparent;
      padding: calc(0.25rem * 0.5);
      transition: opacity 0.2s, rotate 0.4s;
      border: none;
      &:focus {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
      &:nth-child(2) {
        color: var(--color-base-100);
        rotate: 0deg;
      }
      &:nth-child(3) {
        color: var(--color-base-100);
        opacity: 0%;
        rotate: -15deg;
      }
    }
    &:has(:checked) {
      > :nth-child(2) {
        opacity: 0%;
        rotate: 15deg;
      }
      > :nth-child(3) {
        opacity: 100%;
        rotate: 0deg;
      }
    }
    &:before {
      position: relative;
      inset-inline-start: calc(0.25rem * 0);
      grid-column-start: 2;
      grid-row-start: 1;
      aspect-ratio: 1 / 1;
      height: 100%;
      border-radius: var(--radius-selector);
      background-color: currentColor;
      translate: 0;
      --tw-content: "";
      content: var(--tw-content);
      transition: background-color 0.1s, translate 0.2s, inset-inline-start 0.2s;
      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px currentColor;
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000);
      }
      background-size: auto, calc(var(--noise) * 100%);
      background-image: none, var(--fx-noise);
    }
    @media (forced-colors: active) {
      &:before {
        outline-style: var(--tw-outline-style);
        outline-width: 1px;
        outline-offset: calc(1px * -1);
      }
    }
    @media print {
      &:before {
        outline: 0.25rem solid;
        outline-offset: -1rem;
      }
    }
    &:focus-visible, &:has(:focus-visible) {
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"], &:has(> input:checked) {
      grid-template-columns: 1fr 1fr 0fr;
      background-color: var(--color-base-100);
      --input-color: var(--color-base-content);
      &:before {
        background-color: currentColor;
      }
      @starting-style {
        &:before {
          opacity: 0;
        }
      }
    }
    &:indeterminate {
      grid-template-columns: 0.5fr 1fr 0.5fr;
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 30%;
      &:before {
        background-color: transparent;
        border: var(--border) solid currentColor;
      }
    }
  }
  .input {
    cursor: text;
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 2);
    background-color: var(--color-base-100);
    padding-inline: calc(0.25rem * 3);
    vertical-align: middle;
    white-space: nowrap;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    border-color: var(--input-color);
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    &:where(input) {
      display: inline-flex;
    }
    :where(input) {
      display: inline-flex;
      height: 100%;
      width: 100%;
      appearance: none;
      background-color: transparent;
      border: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where(input[type="date"]) {
      display: inline-block;
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> input[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
      box-shadow: none;
    }
    &:has(> input[disabled]) > input[disabled] {
      cursor: not-allowed;
    }
    &::-webkit-date-and-time-value {
      text-align: inherit;
    }
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
    &::-webkit-calendar-picker-indicator {
      position: absolute;
      inset-inline-end: 0.75em;
    }
  }
  .table {
    font-size: 0.875rem;
    position: relative;
    width: 100%;
    border-radius: var(--radius-box);
    text-align: left;
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      text-align: right;
    }
    tr.row-hover {
      &, &:nth-child(even) {
        &:hover {
          @media (hover: hover) {
            background-color: var(--color-base-200);
          }
        }
      }
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 3);
      vertical-align: middle;
    }
    :where(thead, tfoot) {
      white-space: nowrap;
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
      }
      font-size: 0.875rem;
      font-weight: 600;
    }
    :where(tfoot) {
      border-top: var(--border) solid var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        border-top: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
      }
    }
    :where(.table-pin-rows thead tr) {
      position: sticky;
      top: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    :where(.table-pin-rows tfoot tr) {
      position: sticky;
      bottom: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    :where(.table-pin-cols tr th) {
      position: sticky;
      right: calc(0.25rem * 0);
      left: calc(0.25rem * 0);
      background-color: var(--color-base-100);
    }
    :where(thead tr, tbody tr:not(:last-child)) {
      border-bottom: var(--border) solid var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        border-bottom: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
      }
    }
  }
  .select {
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    background-color: var(--color-base-100);
    padding-inline-start: calc(0.25rem * 4);
    padding-inline-end: calc(0.25rem * 7);
    vertical-align: middle;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-image: linear-gradient(45deg, #0000 50%, currentColor 50%), linear-gradient(135deg, currentColor 50%, #0000 50%);
    background-position: calc(100% - 20px) calc(1px + 50%), calc(100% - 16.1px) calc(1px + 50%);
    background-size: 4px 4px, 4px 4px;
    background-repeat: no-repeat;
    text-overflow: ellipsis;
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    border-color: var(--input-color);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    [dir="rtl"] & {
      background-position: calc(0% + 12px) calc(1px + 50%), calc(0% + 16px) calc(1px + 50%);
    }
    select {
      margin-inline-start: calc(0.25rem * -4);
      margin-inline-end: calc(0.25rem * -7);
      width: calc(100% + 2.75rem);
      appearance: none;
      padding-inline-start: calc(0.25rem * 4);
      padding-inline-end: calc(0.25rem * 7);
      height: calc(100% - 2px);
      background: inherit;
      border-radius: inherit;
      border-style: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
      &:not(:last-child) {
        margin-inline-end: calc(0.25rem * -5.5);
        background-image: none;
      }
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> select[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
    }
    &:has(> select[disabled]) > select[disabled] {
      cursor: not-allowed;
    }
  }
  .swap {
    position: relative;
    display: inline-grid;
    cursor: pointer;
    place-content: center;
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    input {
      appearance: none;
      border: none;
    }
    > * {
      grid-column-start: 1;
      grid-row-start: 1;
      transition-property: transform, rotate, opacity;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
    .swap-on, .swap-indeterminate, input:indeterminate ~ .swap-on {
      opacity: 0%;
    }
    input:is(:checked, :indeterminate) {
      & ~ .swap-off {
        opacity: 0%;
      }
    }
    input:checked ~ .swap-on, input:indeterminate ~ .swap-indeterminate {
      opacity: 100%;
      backface-visibility: visible;
    }
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .checkbox {
    border: var(--border) solid var(--input-color, var(--color-base-content));
    @supports (color: color-mix(in lab, red, red)) {
      border: var(--border) solid var(--input-color, color-mix(in oklab, var(--color-base-content) 20%, #0000));
    }
    position: relative;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    border-radius: var(--radius-selector);
    padding: calc(0.25rem * 1);
    vertical-align: middle;
    color: var(--color-base-content);
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 0 #0000 inset, 0 0 #0000;
    transition: background-color 0.2s, box-shadow 0.2s;
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: var(--size);
    height: var(--size);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    &:before {
      --tw-content: "";
      content: var(--tw-content);
      display: block;
      width: 100%;
      height: 100%;
      rotate: 45deg;
      background-color: currentColor;
      opacity: 0%;
      transition: clip-path 0.3s, opacity 0.1s, rotate 0.3s, translate 0.3s;
      transition-delay: 0.1s;
      clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 70% 80%, 70% 100%);
      box-shadow: 0px 3px 0 0px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
      font-size: 1rem;
      line-height: 0.75;
    }
    &:focus-visible {
      outline: 2px solid var(--input-color, currentColor);
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"] {
      background-color: var(--input-color, #0000);
      box-shadow: 0 0 #0000 inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));
      &:before {
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 0%, 70% 0%, 70% 100%);
        opacity: 100%;
      }
      @media (forced-colors: active) {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
      @media print {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
    }
    &:indeterminate {
      &:before {
        rotate: 0deg;
        opacity: 100%;
        translate: 0 -35%;
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 80% 80%, 80% 100%);
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 20%;
    }
  }
  .radio {
    position: relative;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    border-radius: calc(infinity * 1px);
    padding: calc(0.25rem * 1);
    vertical-align: middle;
    border: var(--border) solid var(--input-color, currentColor);
    @supports (color: color-mix(in lab, red, red)) {
      border: var(--border) solid var(--input-color, color-mix(in srgb, currentColor 20%, #0000));
    }
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset;
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: var(--size);
    height: var(--size);
    color: var(--input-color, currentColor);
    &:before {
      display: block;
      width: 100%;
      height: 100%;
      border-radius: calc(infinity * 1px);
      --tw-content: "";
      content: var(--tw-content);
      background-size: auto, calc(var(--noise) * 100%);
      background-image: none, var(--fx-noise);
    }
    &:focus-visible {
      outline: 2px solid currentColor;
    }
    &:checked, &[aria-checked="true"] {
      animation: radio 0.2s ease-out;
      border-color: currentColor;
      background-color: var(--color-base-100);
      &:before {
        background-color: currentColor;
        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));
      }
      @media (forced-colors: active) {
        &:before {
          outline-style: var(--tw-outline-style);
          outline-width: 1px;
          outline-offset: calc(1px * -1);
        }
      }
      @media print {
        &:before {
          outline: 0.25rem solid;
          outline-offset: -1rem;
        }
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 20%;
    }
  }
  .rating {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
    & input {
      border: none;
      appearance: none;
    }
    :where(*) {
      animation: rating 0.25s ease-out;
      height: calc(0.25rem * 6);
      width: calc(0.25rem * 6);
      border-radius: 0;
      background-color: var(--color-base-content);
      opacity: 20%;
      &:is(input) {
        cursor: pointer;
      }
    }
    & .rating-hidden {
      width: calc(0.25rem * 2);
      background-color: transparent;
    }
    input[type="radio"]:checked {
      background-image: none;
    }
    * {
      &:checked, &[aria-checked="true"], &[aria-current="true"], &:has(~ *:checked, ~ *[aria-checked="true"], ~ *[aria-current="true"]) {
        opacity: 100%;
      }
      &:focus-visible {
        transition: scale 0.2s ease-out;
        scale: 1.1;
      }
    }
    & *:active:focus {
      animation: none;
      scale: 1.1;
    }
    &.rating-xs :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 4);
      height: calc(0.25rem * 4);
    }
    &.rating-sm :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 5);
      height: calc(0.25rem * 5);
    }
    &.rating-md :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 6);
      height: calc(0.25rem * 6);
    }
    &.rating-lg :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 7);
      height: calc(0.25rem * 7);
    }
    &.rating-xl :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 8);
      height: calc(0.25rem * 8);
    }
  }
  .drawer {
    position: relative;
    display: grid;
    width: 100%;
    grid-auto-columns: max-content auto;
  }
  .progress {
    position: relative;
    height: calc(0.25rem * 2);
    width: 100%;
    appearance: none;
    overflow: hidden;
    border-radius: var(--radius-box);
    background-color: currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, currentColor 20%, transparent);
    }
    color: var(--color-base-content);
    &:indeterminate {
      background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );
      background-size: 200%;
      background-position-x: 15%;
      animation: progress 5s ease-in-out infinite;
      @supports (-moz-appearance: none) {
        &::-moz-progress-bar {
          background-color: transparent;
          background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );
          background-size: 200%;
          background-position-x: 15%;
          animation: progress 5s ease-in-out infinite;
        }
      }
    }
    @supports (-moz-appearance: none) {
      &::-moz-progress-bar {
        border-radius: var(--radius-box);
        background-color: currentColor;
      }
    }
    @supports (-webkit-appearance: none) {
      &::-webkit-progress-bar {
        border-radius: var(--radius-box);
        background-color: transparent;
      }
      &::-webkit-progress-value {
        border-radius: var(--radius-box);
        background-color: currentColor;
      }
    }
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .-top-16 {
    top: calc(var(--spacing) * -16);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1 {
    top: calc(var(--spacing) * 1);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1 {
    left: calc(var(--spacing) * 1);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .file-input {
    cursor: pointer;
    cursor: pointer;
    border: var(--border) solid #0000;
    display: inline-flex;
    appearance: none;
    align-items: center;
    background-color: var(--color-base-100);
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    padding-inline-end: 0.75rem;
    font-size: 0.875rem;
    line-height: 2;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    border-color: var(--input-color);
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    &::file-selector-button {
      margin-inline-end: calc(0.25rem * 4);
      cursor: pointer;
      padding-inline: calc(0.25rem * 4);
      webkit-user-select: none;
      user-select: none;
      height: calc(100% + var(--border) * 2);
      margin-block: calc(var(--border) * -1);
      margin-inline-start: calc(var(--border) * -1);
      font-size: 0.875rem;
      color: var(--btn-fg);
      border-width: var(--border);
      border-style: solid;
      border-color: var(--btn-border);
      border-start-start-radius: calc(var(--join-ss, var(--radius-field) - var(--border)));
      border-end-start-radius: calc(var(--join-es, var(--radius-field) - var(--border)));
      font-weight: 600;
      background-color: var(--btn-bg);
      background-size: calc(var(--noise) * 100%);
      background-image: var(--btn-noise);
      text-shadow: 0 0.5px oklch(1 0 0 / calc(var(--depth) * 0.15));
      box-shadow: 0 0.5px 0 0.5px white inset, var(--btn-shadow);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 0.5px 0 0.5px color-mix( in oklab, color-mix(in oklab, white 30%, var(--btn-bg)) calc(var(--depth) * 20%), #0000 ) inset, var(--btn-shadow);
      }
      --size: calc(var(--size-field, 0.25rem) * 10);
      --btn-bg: var(--btn-color, var(--color-base-200));
      --btn-fg: var(--color-base-content);
      --btn-border: var(--btn-bg);
      @supports (color: color-mix(in lab, red, red)) {
        --btn-border: color-mix(in oklab, var(--btn-bg), #000 5%);
      }
      --btn-shadow: 0 3px 2px -2px var(--btn-bg),
      0 4px 3px -2px var(--btn-bg);
      @supports (color: color-mix(in lab, red, red)) {
        --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) 30%, #0000),
      0 4px 3px -2px color-mix(in oklab, var(--btn-bg) 30%, #0000);
      }
      --btn-noise: var(--fx-noise);
    }
    &:focus {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) 10%, #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
    }
    &:has(> input[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
      box-shadow: none;
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      }
      &::file-selector-button {
        cursor: not-allowed;
        border-color: var(--color-base-200);
        background-color: var(--color-base-200);
        --btn-border: #0000;
        --btn-noise: none;
        --btn-fg: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
        }
      }
    }
  }
  .textarea {
    border: var(--border) solid #0000;
    min-height: calc(0.25rem * 20);
    flex-shrink: 1;
    appearance: none;
    border-radius: var(--radius-field);
    background-color: var(--color-base-100);
    padding-block: calc(0.25rem * 2);
    vertical-align: middle;
    width: clamp(3rem, 20rem, 100%);
    padding-inline-start: 0.75rem;
    padding-inline-end: 0.75rem;
    font-size: 0.875rem;
    border-color: var(--input-color);
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    textarea {
      appearance: none;
      background-color: transparent;
      border: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
    }
    &:has(> textarea[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
      box-shadow: none;
    }
    &:has(> textarea[disabled]) > textarea[disabled] {
      cursor: not-allowed;
    }
  }
  .btn-active {
    --btn-bg: var(--btn-color, var(--color-base-200));
    @supports (color: color-mix(in lab, red, red)) {
      --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
    }
    --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    isolation: isolate;
  }
  .z-10 {
    z-index: 10;
  }
  .z-30 {
    z-index: 30;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .tab-content {
    order: var(--tabcontent-order);
    display: none;
    border-color: transparent;
    --tabcontent-radius-ss: 0;
    --tabcontent-radius-se: 0;
    --tabcontent-radius-es: 0;
    --tabcontent-radius-ee: 0;
    --tabcontent-order: 1;
    width: 100%;
    margin: var(--tabcontent-margin);
    border-width: var(--border);
    border-start-start-radius: var(--tabcontent-radius-ss);
    border-start-end-radius: var(--tabcontent-radius-se);
    border-end-start-radius: var(--tabcontent-radius-es);
    border-end-end-radius: var(--tabcontent-radius-ee);
  }
  .modal-box {
    grid-column-start: 1;
    grid-row-start: 1;
    max-height: 100vh;
    width: calc(11/12 * 100%);
    max-width: 32rem;
    background-color: var(--color-base-100);
    padding: calc(0.25rem * 6);
    transition: translate 0.3s ease-out, scale 0.3s ease-out, opacity 0.2s ease-out 0.05s, box-shadow 0.3s ease-out;
    border-top-left-radius: var(--modal-tl, var(--radius-box));
    border-top-right-radius: var(--modal-tr, var(--radius-box));
    border-bottom-left-radius: var(--modal-bl, var(--radius-box));
    border-bottom-right-radius: var(--modal-br, var(--radius-box));
    scale: 95%;
    opacity: 0;
    box-shadow: oklch(0% 0 0/ 0.25) 0px 25px 50px -12px;
    overflow-y: auto;
    overscroll-behavior: contain;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .m-10 {
    margin: calc(var(--spacing) * 10);
  }
  .m-auto {
    margin: auto;
  }
  .filter {
    display: flex;
    flex-wrap: wrap;
    input[type="radio"] {
      width: auto;
    }
    input {
      overflow: hidden;
      opacity: 100%;
      scale: 1;
      transition: margin 0.1s, opacity 0.3s, padding 0.3s, border-width 0.1s;
      &:not(:last-child) {
        margin-inline-end: calc(0.25rem * 1);
      }
      &.filter-reset {
        aspect-ratio: 1 / 1;
        &::after {
          content: "×";
        }
      }
    }
    &:not(:has(input:checked:not(.filter-reset))) {
      .filter-reset, input[type="reset"] {
        scale: 0;
        border-width: 0;
        margin-inline: calc(0.25rem * 0);
        width: calc(0.25rem * 0);
        padding-inline: calc(0.25rem * 0);
        opacity: 0%;
      }
    }
    &:has(input:checked:not(.filter-reset)) {
      input:not(:checked, .filter-reset, input[type="reset"]) {
        scale: 0;
        border-width: 0;
        margin-inline: calc(0.25rem * 0);
        width: calc(0.25rem * 0);
        padding-inline: calc(0.25rem * 0);
        opacity: 0%;
      }
    }
  }
  .mx-auto {
    margin-inline: auto;
  }
  .file-input-ghost {
    background-color: transparent;
    transition: background-color 0.2s;
    box-shadow: none;
    border-color: #0000;
    &::file-selector-button {
      margin-inline-start: calc(0.25rem * 0);
      margin-inline-end: calc(0.25rem * 4);
      height: 100%;
      cursor: pointer;
      padding-inline: calc(0.25rem * 4);
      webkit-user-select: none;
      user-select: none;
      margin-block: 0;
      border-start-end-radius: calc(var(--join-ss, var(--radius-field) - var(--border)));
      border-end-end-radius: calc(var(--join-es, var(--radius-field) - var(--border)));
    }
    &:focus, &:focus-within {
      background-color: var(--color-base-100);
      color: var(--color-base-content);
      border-color: #0000;
      box-shadow: none;
    }
  }
  .my-0 {
    margin-block: calc(var(--spacing) * 0);
  }
  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .label {
    display: inline-flex;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    white-space: nowrap;
    color: currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, currentColor 60%, transparent);
    }
    &:has(input) {
      cursor: pointer;
    }
    &:is(.input > *, .select > *) {
      display: flex;
      height: calc(100% - 0.5rem);
      align-items: center;
      padding-inline: calc(0.25rem * 3);
      white-space: nowrap;
      font-size: inherit;
      &:first-child {
        margin-inline-start: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * 3);
        border-inline-end: var(--border) solid currentColor;
        @supports (color: color-mix(in lab, red, red)) {
          border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
        }
      }
      &:last-child {
        margin-inline-start: calc(0.25rem * 3);
        margin-inline-end: calc(0.25rem * -3);
        border-inline-start: var(--border) solid currentColor;
        @supports (color: color-mix(in lab, red, red)) {
          border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
        }
      }
    }
  }
  .join-item {
    &:where(*:not(:first-child, :disabled, [disabled], .btn-disabled)) {
      margin-inline-start: calc(var(--border, 1px) * -1);
      margin-block-start: 0;
    }
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }
  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }
  .mt-24 {
    margin-top: calc(var(--spacing) * 24);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .fieldset-legend {
    margin-bottom: calc(0.25rem * -1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: calc(0.25rem * 2);
    padding-block: calc(0.25rem * 2);
    color: var(--color-base-content);
    font-weight: 600;
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }
  .mb-32 {
    margin-bottom: calc(var(--spacing) * 32);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .status {
    display: inline-block;
    aspect-ratio: 1 / 1;
    width: calc(0.25rem * 2);
    height: calc(0.25rem * 2);
    border-radius: var(--radius-selector);
    background-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
    color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in srgb, #000 30%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-black) 30%, transparent);
      }
    }
    background-image: radial-gradient( circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * 0.5)), #0000 );
    box-shadow: 0 2px 3px -1px currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000);
    }
  }
  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 2);
    border-radius: var(--radius-selector);
    vertical-align: middle;
    color: var(--badge-fg);
    border: var(--border) solid var(--badge-color, var(--color-base-200));
    font-size: 0.875rem;
    width: fit-content;
    padding-inline: calc(0.25rem * 3 - var(--border));
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    background-color: var(--badge-bg);
    --badge-bg: var(--badge-color, var(--color-base-100));
    --badge-fg: var(--color-base-content);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    height: var(--size);
    &.badge-outline {
      --badge-fg: var(--badge-color);
      --badge-bg: #0000;
      background-image: none;
      border-color: currentColor;
    }
    &.badge-dash {
      --badge-fg: var(--badge-color);
      --badge-bg: #0000;
      background-image: none;
      border-color: currentColor;
      border-style: dashed;
    }
    &.badge-soft {
      color: var(--badge-color, var(--color-base-content));
      background-color: var(--badge-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix( in oklab, var(--badge-color, var(--color-base-content)) 8%, var(--color-base-100) );
      }
      border-color: var(--badge-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix( in oklab, var(--badge-color, var(--color-base-content)) 10%, var(--color-base-100) );
      }
      background-image: none;
    }
  }
  .tabs {
    display: flex;
    flex-wrap: wrap;
    --tabs-height: auto;
    --tabs-direction: row;
    height: var(--tabs-height);
    flex-direction: var(--tabs-direction);
  }
  .fieldset {
    display: grid;
    gap: calc(0.25rem * 1.5);
    padding-block: calc(0.25rem * 1);
    font-size: 0.75rem;
    grid-template-columns: 1fr;
    grid-auto-rows: max-content;
  }
  .join {
    display: inline-flex;
    align-items: stretch;
    --join-ss: 0;
    --join-se: 0;
    --join-es: 0;
    --join-ee: 0;
    :where(.join-item) {
      border-start-start-radius: var(--join-ss, 0);
      border-start-end-radius: var(--join-se, 0);
      border-end-start-radius: var(--join-es, 0);
      border-end-end-radius: var(--join-ee, 0);
      * {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
    > .join-item:where(:first-child) {
      --join-ss: var(--radius-field);
      --join-se: 0;
      --join-es: var(--radius-field);
      --join-ee: 0;
    }
    :first-child:not(:last-child) {
      :where(.join-item) {
        --join-ss: var(--radius-field);
        --join-se: 0;
        --join-es: var(--radius-field);
        --join-ee: 0;
      }
    }
    > .join-item:where(:last-child) {
      --join-ss: 0;
      --join-se: var(--radius-field);
      --join-es: 0;
      --join-ee: var(--radius-field);
    }
    :last-child:not(:first-child) {
      :where(.join-item) {
        --join-ss: 0;
        --join-se: var(--radius-field);
        --join-es: 0;
        --join-ee: var(--radius-field);
      }
    }
    > .join-item:where(:only-child) {
      --join-ss: var(--radius-field);
      --join-se: var(--radius-field);
      --join-es: var(--radius-field);
      --join-ee: var(--radius-field);
    }
    :only-child {
      :where(.join-item) {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
  }
  .chat {
    display: grid;
    column-gap: calc(0.25rem * 3);
    padding-block: calc(0.25rem * 1);
  }
  .prose {
    :root & {
      --tw-prose-body: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-body: color-mix(in oklab, var(--color-base-content) 80%, #0000);
      }
      --tw-prose-headings: var(--color-base-content);
      --tw-prose-lead: var(--color-base-content);
      --tw-prose-links: var(--color-base-content);
      --tw-prose-bold: var(--color-base-content);
      --tw-prose-counters: var(--color-base-content);
      --tw-prose-bullets: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-bullets: color-mix(in oklab, var(--color-base-content) 50%, #0000);
      }
      --tw-prose-hr: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-hr: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      }
      --tw-prose-quotes: var(--color-base-content);
      --tw-prose-quote-borders: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-quote-borders: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      }
      --tw-prose-captions: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-captions: color-mix(in oklab, var(--color-base-content) 50%, #0000);
      }
      --tw-prose-code: var(--color-base-content);
      --tw-prose-pre-code: var(--color-neutral-content);
      --tw-prose-pre-bg: var(--color-neutral);
      --tw-prose-th-borders: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-th-borders: color-mix(in oklab, var(--color-base-content) 50%, #0000);
      }
      --tw-prose-td-borders: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-td-borders: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      }
      --tw-prose-kbd: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-kbd: color-mix(in oklab, var(--color-base-content) 80%, #0000);
      }
      :where(code):not(pre > code) {
        background-color: var(--color-base-200);
        border-radius: var(--radius-selector);
        border: var(--border) solid var(--color-base-300);
        padding-inline: 0.5em;
        font-weight: inherit;
        &:before, &:after {
          display: none;
        }
      }
    }
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }
  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }
  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-40 {
    height: calc(var(--spacing) * 40);
  }
  .h-48 {
    height: calc(var(--spacing) * 48);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-\[1em\] {
    height: 1em;
  }
  .h-\[calc\(100\%-8rem\)\] {
    height: calc(100% - 8rem);
  }
  .h-\[calc\(100vh-4rem\)\] {
    height: calc(100vh - 4rem);
  }
  .h-full {
    height: 100%;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-fit {
    max-height: fit-content;
  }
  .min-h-11 {
    min-height: calc(var(--spacing) * 11);
  }
  .min-h-16 {
    min-height: calc(var(--spacing) * 16);
  }
  .min-h-32 {
    min-height: calc(var(--spacing) * 32);
  }
  .min-h-64 {
    min-height: calc(var(--spacing) * 64);
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-1 {
    width: calc(var(--spacing) * 1);
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-1\/4 {
    width: calc(1/4 * 100%);
  }
  .w-1\/5 {
    width: calc(1/5 * 100%);
  }
  .w-1\/6 {
    width: calc(1/6 * 100%);
  }
  .w-1\/8 {
    width: calc(1/8 * 100%);
  }
  .w-1\/12 {
    width: calc(1/12 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\/3 {
    width: calc(2/3 * 100%);
  }
  .w-2\/4 {
    width: calc(2/4 * 100%);
  }
  .w-2\/12 {
    width: calc(2/12 * 100%);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\/4 {
    width: calc(3/4 * 100%);
  }
  .w-3\/12 {
    width: calc(3/12 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-4\/12 {
    width: calc(4/12 * 100%);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-5\/12 {
    width: calc(5/12 * 100%);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-11 {
    width: calc(var(--spacing) * 11);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-36 {
    width: calc(var(--spacing) * 36);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-fit {
    width: fit-content;
  }
  .w-full {
    width: 100%;
  }
  .max-w-\[149px\] {
    max-width: 149px;
  }
  .max-w-\[500px\] {
    max-width: 500px;
  }
  .max-w-\[600px\] {
    max-width: 600px;
  }
  .max-w-\[1120px\] {
    max-width: 1120px;
  }
  .max-w-\[1440px\] {
    max-width: 1440px;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-lg {
    max-width: var(--container-lg);
  }
  .min-w-32 {
    min-width: calc(var(--spacing) * 32);
  }
  .min-w-64 {
    min-width: calc(var(--spacing) * 64);
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .grow {
    flex-grow: 1;
  }
  .table-fixed {
    table-layout: fixed;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .-translate-x-1 {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1 {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .rotate-90 {
    rotate: 90deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .skeleton {
    border-radius: var(--radius-box);
    background-color: var(--color-base-300);
    @media (prefers-reduced-motion: reduce) {
      transition-duration: 15s;
    }
    will-change: background-position;
    animation: skeleton 1.8s ease-in-out infinite;
    background-image: linear-gradient( 105deg, #0000 0% 40%, var(--color-base-100) 50%, #0000 60% 100% );
    background-size: 200% auto;
    background-repeat: no-repeat;
    background-position-x: -50%;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .resize-none {
    resize: none;
  }
  .appearance-none {
    appearance: none;
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-col-reverse {
    flex-direction: column-reverse;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .content-center {
    align-content: center;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-visible {
    overflow: visible;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .tabs-box {
    background-color: var(--color-base-200);
    padding: calc(0.25rem * 1);
    --tabs-box-radius: calc(var(--radius-field) + var(--radius-field) + var(--radius-field));
    border-radius: calc(var(--radius-field) + min(0.25rem, var(--tabs-box-radius)));
    box-shadow: 0 -0.5px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 0.5px oklch(0% 0 0 / calc(var(--depth) * 0.05)) inset;
    .tab {
      border-radius: var(--radius-field);
      border-style: none;
      &:focus-visible, &:is(label:has(:checked:focus-visible)) {
        outline-offset: 2px;
      }
    }
    > :is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), > :is(input:checked), > :is(label:has(:checked)) {
      background-color: var(--tab-bg, var(--color-base-100));
      box-shadow: 0 1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px 1px -1px var(--color-neutral), 0 1px 6px -4px var(--color-neutral);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px 1px -1px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 50%), #0000), 0 1px 6px -4px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 100%), #0000);
      }
      @media (forced-colors: active) {
        border: 1px solid;
      }
    }
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-t-md {
    border-top-left-radius: var(--radius-md);
    border-top-right-radius: var(--radius-md);
  }
  .rounded-tl-md {
    border-top-left-radius: var(--radius-md);
  }
  .rounded-b-lg {
    border-bottom-right-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .rounded-b-md {
    border-bottom-right-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-1 {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-t-0 {
    border-top-style: var(--tw-border-style);
    border-top-width: 0px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-1 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-l-2 {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .badge-ghost {
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
    background-image: none;
  }
  .border-black {
    border-color: var(--color-black);
  }
  .border-blue-100 {
    border-color: var(--color-blue-100);
  }
  .border-blue-300 {
    border-color: var(--color-blue-300);
  }
  .border-gray-50 {
    border-color: var(--color-gray-50);
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-gray-400 {
    border-color: var(--color-gray-400);
  }
  .border-gray-500 {
    border-color: var(--color-gray-500);
  }
  .border-slate-300 {
    border-color: var(--color-slate-300);
  }
  .border-transparent {
    border-color: transparent;
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-black\/50 {
    background-color: color-mix(in srgb, #000 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }
  .bg-slate-50 {
    background-color: var(--color-slate-50);
  }
  .bg-slate-100 {
    background-color: var(--color-slate-100);
  }
  .bg-slate-200 {
    background-color: var(--color-slate-200);
  }
  .bg-slate-800 {
    background-color: var(--color-slate-800);
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .mask-repeat {
    mask-repeat: repeat;
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .pt-10 {
    padding-top: calc(var(--spacing) * 10);
  }
  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }
  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }
  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }
  .pb-32 {
    padding-bottom: calc(var(--spacing) * 32);
  }
  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }
  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }
  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .align-middle {
    vertical-align: middle;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .text-nowrap {
    text-wrap: nowrap;
  }
  .text-wrap {
    text-wrap: wrap;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-slate-800 {
    color: var(--color-slate-800);
  }
  .text-white {
    color: var(--color-white);
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .underline {
    text-decoration-line: underline;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .shadow-\[1px_0_0px_0_rgba\(0\,0\,0\,0\.05\)\] {
    --tw-shadow: 1px 0 0px 0 var(--tw-shadow-color, rgba(0,0,0,0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-blue-400\/10 {
    --tw-shadow-color: color-mix(in srgb, oklch(70.7% 0.165 254.624) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-400) 10%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .btn-ghost {
    &:not(.btn-active, :hover, :active:focus, :focus-visible) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-border: #0000;
      --btn-noise: none;
      &:not(:disabled, [disabled], .btn-disabled) {
        outline-color: currentColor;
        --btn-fg: currentColor;
      }
    }
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .btn-outline {
    &:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-fg: var(--btn-color);
      --btn-border: var(--btn-color);
      --btn-noise: none;
    }
    @media (hover: none) {
      &:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
        --btn-shadow: "";
        --btn-bg: #0000;
        --btn-fg: var(--btn-color);
        --btn-border: var(--btn-color);
        --btn-noise: none;
      }
    }
  }
  .btn-soft {
    &:not(.btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled) {
      --btn-shadow: "";
      --btn-fg: var(--btn-color, var(--color-base-content));
      --btn-bg: var(--btn-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-bg: color-mix(
      in oklab,
      var(--btn-color, var(--color-base-content)) 8%,
      var(--color-base-100)
    );
      }
      --btn-border: var(--btn-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-border: color-mix(
      in oklab,
      var(--btn-color, var(--color-base-content)) 10%,
      var(--color-base-100)
    );
      }
      --btn-noise: none;
    }
    @media (hover: none) {
      &:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {
        --btn-shadow: "";
        --btn-fg: var(--btn-color, var(--color-base-content));
        --btn-bg: var(--btn-color, var(--color-base-content));
        @supports (color: color-mix(in lab, red, red)) {
          --btn-bg: color-mix(
        in oklab,
        var(--btn-color, var(--color-base-content)) 8%,
        var(--color-base-100)
      );
        }
        --btn-border: var(--btn-color, var(--color-base-content));
        @supports (color: color-mix(in lab, red, red)) {
          --btn-border: color-mix(
        in oklab,
        var(--btn-color, var(--color-base-content)) 10%,
        var(--color-base-100)
      );
        }
        --btn-noise: none;
      }
    }
  }
  .btn-sm {
    --fontsize: 0.75rem;
    --btn-p: 0.75rem;
    --size: calc(var(--size-field, 0.25rem) * 8);
  }
  .btn-xs {
    --fontsize: 0.6875rem;
    --btn-p: 0.5rem;
    --size: calc(var(--size-field, 0.25rem) * 6);
  }
  .badge-error {
    --badge-color: var(--color-error);
    --badge-fg: var(--color-error-content);
  }
  .badge-info {
    --badge-color: var(--color-info);
    --badge-fg: var(--color-info-content);
  }
  .badge-primary {
    --badge-color: var(--color-primary);
    --badge-fg: var(--color-primary-content);
  }
  .btn-primary {
    --btn-color: var(--color-primary);
    --btn-fg: var(--color-primary-content);
  }
  .group-hover\:visible {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        visibility: visible;
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .peer-checked\:bg-blue-600 {
    &:is(:where(.peer):checked ~ *) {
      background-color: var(--color-blue-600);
    }
  }
  .peer-checked\:bg-slate-800 {
    &:is(:where(.peer):checked ~ *) {
      background-color: var(--color-slate-800);
    }
  }
  .peer-focus\:ring-2 {
    &:is(:where(.peer):focus ~ *) {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .peer-focus\:ring-4 {
    &:is(:where(.peer):focus ~ *) {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .peer-focus\:ring-blue-300 {
    &:is(:where(.peer):focus ~ *) {
      --tw-ring-color: var(--color-blue-300);
    }
  }
  .peer-focus\:ring-slate-300 {
    &:is(:where(.peer):focus ~ *) {
      --tw-ring-color: var(--color-slate-300);
    }
  }
  .peer-focus\:outline-none {
    &:is(:where(.peer):focus ~ *) {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .after\:absolute {
    &::after {
      content: var(--tw-content);
      position: absolute;
    }
  }
  .after\:start-\[2px\] {
    &::after {
      content: var(--tw-content);
      inset-inline-start: 2px;
    }
  }
  .after\:top-\[2px\] {
    &::after {
      content: var(--tw-content);
      top: 2px;
    }
  }
  .after\:left-\[2px\] {
    &::after {
      content: var(--tw-content);
      left: 2px;
    }
  }
  .after\:h-5 {
    &::after {
      content: var(--tw-content);
      height: calc(var(--spacing) * 5);
    }
  }
  .after\:w-5 {
    &::after {
      content: var(--tw-content);
      width: calc(var(--spacing) * 5);
    }
  }
  .after\:rounded-full {
    &::after {
      content: var(--tw-content);
      border-radius: calc(infinity * 1px);
    }
  }
  .after\:border {
    &::after {
      content: var(--tw-content);
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .after\:border-gray-300 {
    &::after {
      content: var(--tw-content);
      border-color: var(--color-gray-300);
    }
  }
  .after\:bg-white {
    &::after {
      content: var(--tw-content);
      background-color: var(--color-white);
    }
  }
  .after\:transition-all {
    &::after {
      content: var(--tw-content);
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
  }
  .after\:content-\[\'\'\] {
    &::after {
      content: var(--tw-content);
      --tw-content: '';
      content: var(--tw-content);
    }
  }
  .peer-checked\:after\:translate-x-full {
    &:is(:where(.peer):checked ~ *) {
      &::after {
        content: var(--tw-content);
        --tw-translate-x: 100%;
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .peer-checked\:after\:border-white {
    &:is(:where(.peer):checked ~ *) {
      &::after {
        content: var(--tw-content);
        border-color: var(--color-white);
      }
    }
  }
  .last\:border-0 {
    &:last-child {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .hover\:cursor-help {
    &:hover {
      @media (hover: hover) {
        cursor: help;
      }
    }
  }
  .hover\:cursor-pointer {
    &:hover {
      @media (hover: hover) {
        cursor: pointer;
      }
    }
  }
  .hover\:border-black {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-black);
      }
    }
  }
  .hover\:border-gray-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-300);
      }
    }
  }
  .hover\:border-slate-400 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-slate-400);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-slate-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-50);
      }
    }
  }
  .hover\:bg-slate-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-100);
      }
    }
  }
  .hover\:bg-slate-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-200);
      }
    }
  }
  .hover\:bg-slate-900 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-900);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:text-red-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-800);
      }
    }
  }
  .hover\:text-slate-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-slate-900);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .focus\:h-32 {
    &:focus {
      height: calc(var(--spacing) * 32);
    }
  }
  .focus\:border-slate-800 {
    &:focus {
      border-color: var(--color-slate-800);
    }
  }
  .focus\:ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-slate-800 {
    &:focus {
      --tw-ring-color: var(--color-slate-800);
    }
  }
  .focus\:outline-gray-100 {
    &:focus {
      outline-color: var(--color-gray-100);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:bg-gray-50 {
    &:disabled {
      background-color: var(--color-gray-50);
    }
  }
  .disabled\:text-gray-700 {
    &:disabled {
      color: var(--color-gray-700);
    }
  }
  .sm\:mt-0 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .sm\:mt-8 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .sm\:block {
    @media (width >= 40rem) {
      display: block;
    }
  }
  .sm\:grid {
    @media (width >= 40rem) {
      display: grid;
    }
  }
  .sm\:hidden {
    @media (width >= 40rem) {
      display: none;
    }
  }
  .sm\:max-h-\[80vh\] {
    @media (width >= 40rem) {
      max-height: 80vh;
    }
  }
  .sm\:max-h-\[250px\] {
    @media (width >= 40rem) {
      max-height: 250px;
    }
  }
  .sm\:min-h-\[70vh\] {
    @media (width >= 40rem) {
      min-height: 70vh;
    }
  }
  .sm\:min-h-\[100px\] {
    @media (width >= 40rem) {
      min-height: 100px;
    }
  }
  .sm\:w-1\/4 {
    @media (width >= 40rem) {
      width: calc(1/4 * 100%);
    }
  }
  .sm\:w-1\/5 {
    @media (width >= 40rem) {
      width: calc(1/5 * 100%);
    }
  }
  .sm\:w-8\/12 {
    @media (width >= 40rem) {
      width: calc(8/12 * 100%);
    }
  }
  .sm\:w-9\/12 {
    @media (width >= 40rem) {
      width: calc(9/12 * 100%);
    }
  }
  .sm\:max-w-\[400px\] {
    @media (width >= 40rem) {
      max-width: 400px;
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:items-center {
    @media (width >= 40rem) {
      align-items: center;
    }
  }
  .sm\:gap-0 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 0);
    }
  }
  .sm\:rounded-lg {
    @media (width >= 40rem) {
      border-radius: var(--radius-lg);
    }
  }
  .sm\:rounded-b-lg {
    @media (width >= 40rem) {
      border-bottom-right-radius: var(--radius-lg);
      border-bottom-left-radius: var(--radius-lg);
    }
  }
  .sm\:p-6 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .sm\:text-right {
    @media (width >= 40rem) {
      text-align: right;
    }
  }
  .md\:mt-0 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .md\:mt-8 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .md\:mt-24 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 24);
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:grid {
    @media (width >= 48rem) {
      display: grid;
    }
  }
  .md\:ml-0 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 0);
    }
  }
  .md\:w-1\/2 {
    @media (width >= 48rem) {
      width: calc(1/2 * 100%);
    }
  }
  .md\:w-1\/3 {
    @media (width >= 48rem) {
      width: calc(1/3 * 100%);
    }
  }
  .md\:w-1\/4 {
    @media (width >= 48rem) {
      width: calc(1/4 * 100%);
    }
  }
  .md\:w-1\/5 {
    @media (width >= 48rem) {
      width: calc(1/5 * 100%);
    }
  }
  .md\:w-2\/5 {
    @media (width >= 48rem) {
      width: calc(2/5 * 100%);
    }
  }
  .md\:w-3\/4 {
    @media (width >= 48rem) {
      width: calc(3/4 * 100%);
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:flex-col {
    @media (width >= 48rem) {
      flex-direction: column;
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:justify-end {
    @media (width >= 48rem) {
      justify-content: flex-end;
    }
  }
  .md\:justify-start {
    @media (width >= 48rem) {
      justify-content: flex-start;
    }
  }
  .md\:gap-8 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 8);
    }
  }
  .md\:p-4 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .md\:p-6 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .md\:px-2 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .md\:px-4 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .md\:px-8 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .md\:text-right {
    @media (width >= 48rem) {
      text-align: right;
    }
  }
  .md\:text-sm {
    @media (width >= 48rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .lg\:table {
    @media (width >= 64rem) {
      font-size: 0.875rem;
      position: relative;
      width: 100%;
      border-radius: var(--radius-box);
      text-align: left;
      &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
        text-align: right;
      }
      tr.row-hover {
        &, &:nth-child(even) {
          &:hover {
            @media (hover: hover) {
              background-color: var(--color-base-200);
            }
          }
        }
      }
      :where(th, td) {
        padding-inline: calc(0.25rem * 4);
        padding-block: calc(0.25rem * 3);
        vertical-align: middle;
      }
      :where(thead, tfoot) {
        white-space: nowrap;
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
        }
        font-size: 0.875rem;
        font-weight: 600;
      }
      :where(tfoot) {
        border-top: var(--border) solid var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          border-top: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
        }
      }
      :where(.table-pin-rows thead tr) {
        position: sticky;
        top: calc(0.25rem * 0);
        z-index: 1;
        background-color: var(--color-base-100);
      }
      :where(.table-pin-rows tfoot tr) {
        position: sticky;
        bottom: calc(0.25rem * 0);
        z-index: 1;
        background-color: var(--color-base-100);
      }
      :where(.table-pin-cols tr th) {
        position: sticky;
        right: calc(0.25rem * 0);
        left: calc(0.25rem * 0);
        background-color: var(--color-base-100);
      }
      :where(thead tr, tbody tr:not(:last-child)) {
        border-bottom: var(--border) solid var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          border-bottom: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
        }
      }
    }
  }
  .lg\:absolute {
    @media (width >= 64rem) {
      position: absolute;
    }
  }
  .lg\:relative {
    @media (width >= 64rem) {
      position: relative;
    }
  }
  .lg\:top-0 {
    @media (width >= 64rem) {
      top: calc(var(--spacing) * 0);
    }
  }
  .lg\:bottom-0 {
    @media (width >= 64rem) {
      bottom: calc(var(--spacing) * 0);
    }
  }
  .lg\:bottom-18 {
    @media (width >= 64rem) {
      bottom: calc(var(--spacing) * 18);
    }
  }
  .lg\:mt-0 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .lg\:mt-4 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 4);
    }
  }
  .lg\:mt-8 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .lg\:mb-0 {
    @media (width >= 64rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .lg\:ml-8 {
    @media (width >= 64rem) {
      margin-left: calc(var(--spacing) * 8);
    }
  }
  .lg\:ml-64 {
    @media (width >= 64rem) {
      margin-left: calc(var(--spacing) * 64);
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:flex {
    @media (width >= 64rem) {
      display: flex;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:table {
    @media (width >= 64rem) {
      display: table;
    }
  }
  .lg\:w-1\/2 {
    @media (width >= 64rem) {
      width: calc(1/2 * 100%);
    }
  }
  .lg\:w-64 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 64);
    }
  }
  .lg\:w-\[calc\(100\%-16rem\)\] {
    @media (width >= 64rem) {
      width: calc(100% - 16rem);
    }
  }
  .lg\:flex-col {
    @media (width >= 64rem) {
      flex-direction: column;
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:flex-nowrap {
    @media (width >= 64rem) {
      flex-wrap: nowrap;
    }
  }
  .lg\:px-16 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 16);
    }
  }
  .xl\:flex-row {
    @media (width >= 80rem) {
      flex-direction: row;
    }
  }
  .rtl\:peer-checked\:after\:-translate-x-full {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      &:is(:where(.peer):checked ~ *) {
        &::after {
          content: var(--tw-content);
          --tw-translate-x: -100%;
          translate: var(--tw-translate-x) var(--tw-translate-y);
        }
      }
    }
  }
  .\[\&\:\:-webkit-calendar-picker-indicator\]\:hidden {
    &::-webkit-calendar-picker-indicator {
      display: none;
    }
  }
}
.inboxMessage {
  align-content: flex-start;
  align-items: flex-start;
  justify-content: flex-start;
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-300);
  background-color: var(--color-white);
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
}
@layer base {
  :where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not([class*="drawer-open"]) > .drawer-toggle:checked ) {
    overflow: hidden;
  }
}
@layer base {
  :root, [data-theme] {
    background-color: var(--root-bg, var(--color-base-100));
    color: var(--color-base-content);
  }
}
@layer base {
  :root {
    scrollbar-color: currentColor #0000;
    @supports (color: color-mix(in lab, red, red)) {
      scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;
    }
  }
}
@layer base {
  @property --radialprogress {
    syntax: "<percentage>";
    inherits: true;
    initial-value: 0%;
  }
}
@layer base {
  :where( :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked ) ) {
    scrollbar-gutter: stable;
    background-image: linear-gradient(var(--color-base-100), var(--color-base-100));
    --root-bg: var(--color-base-100);
    @supports (color: color-mix(in lab, red, red)) {
      --root-bg: color-mix(in srgb, var(--color-base-100), oklch(0% 0 0) 40%);
    }
  }
  :where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(.modal-start, .modal-end) {
    scrollbar-gutter: stable;
  }
}
@layer base {
  :root {
    --fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E");
  }
  .chat {
    --mask-chat: url("data:image/svg+xml,%3csvg width='13' height='13' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M0 11.5004C0 13.0004 2 13.0004 2 13.0004H12H13V0.00036329L12.5 0C12.5 0 11.977 2.09572 11.8581 2.50033C11.6075 3.35237 10.9149 4.22374 9 5.50036C6 7.50036 0 10.0004 0 11.5004Z'/%3e%3c/svg%3e");
  }
}
@keyframes dropdown {
  0% {
    opacity: 0;
  }
}
@keyframes progress {
  50% {
    background-position-x: -115%;
  }
}
@keyframes toast {
  0% {
    scale: 0.9;
    opacity: 0;
  }
  100% {
    scale: 1;
    opacity: 1;
  }
}
@keyframes rating {
  0%, 40% {
    scale: 1.1;
    filter: brightness(1.05) contrast(1.05);
  }
}
@keyframes radio {
  0% {
    padding: 5px;
  }
  50% {
    padding: 3px;
  }
}
@keyframes skeleton {
  0% {
    background-position: 150%;
  }
  100% {
    background-position: -50%;
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}
