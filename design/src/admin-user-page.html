<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>User Details</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
  </head>
  <body class="min-h-screen bg-gray-200">
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>

      <main class="w-full overflow-y-auto lg:px-16 md:px-8 px-4 bg-slate-100">
        <!-- Page Content-->
        <div class="max-w-[1440px] w-full mx-auto md:mt-24 lg:mt-4">
          <div class="w-full mt-20 md:mt-8 pb-0">
            <a class="text-sm underline" href="./admin.html"
              >← Back to Admin Dashboard</a
            >
          </div>

          <div class="flex flex-row justify-between w-full mt-8 pb-0">
            <h1 class="text-2xl">User Details</h1>
            <a href="admin-log.html"><button class="btn">Admin Logs</button></a>
          </div>

          <!-- User Details-->
          <div
            class="flex flex-col bg-white rounded border border-gray-300 mt-4 w-full p-2 gap-8 md:p-6 text-sm"
          >
            <!-- Account info -->
            <div class="flex flex-1 flex-wrap gap-4">
              <h2 class="text-lg font-bold w-full mb-4">Account Information</h2>
              <div class="flex flex-row w-full">
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Name:</div>
                  <span>John Middlename Smith</span
                  ><span class="ml-1 italic text-gray-500">(**********)</span>
                </div>
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Organization:</div>
                  <span>The Org</span
                  ><span class="ml-1 italic text-gray-500">(Owner)</span>
                </div>
              </div>
              <div class="flex flex-row w-full">
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Email:</div>
                  <span><EMAIL></span>
                </div>
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Phone Number:</div>
                  <span>-</span>
                </div>
              </div>
              <div class="flex flex-row w-full">
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Account Verified:</div>
                  <span>No</span>
                </div>
              </div>

              <fieldset class="fieldset w-full my-4">
                <legend class="fieldset-legend">Account Status</legend>
                <select class="select">
                  <option disabled selected>Active</option>
                  <option>Suspended</option>
                  <option>Disabled</option>
                  <option>-</option>
                </select>
              </fieldset>

              <div class="flex flex-row w-full">
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Billing Address:</div>
                  <div class="flex flex-col">
                    <span>12345 Roadstreet Crescent, Mississauga,</span>
                    <span>ON, X0X 0X0, Canada</span>
                  </div>
                </div>
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Payment Method:</div>
                  <span>VISA1234</span>
                </div>
              </div>

              <a href="admin-billing.html"
                ><button class="btn mt-4">Billing History</button></a
              >
            </div>

            <!-- Org info -->
            <div class="flex flex-1 flex-wrap gap-4">
              <h2 class="text-lg font-bold w-full mb-4">
                Organization Information
              </h2>
              <div class="flex flex-row w-full">
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Account Type:</div>
                  <span class="badge badge-soft">Basic Account</span>
                </div>
              </div>

              <div class="flex flex-row w-full">
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Business Name:</div>
                  <span>The Org</span>
                  <span class="ml-1 italic text-gray-500">(Unverified)</span>
                </div>
              </div>
              <div class="flex flex-row w-full">
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Owner:</div>

                  <span>John Smith</span>
                  <span class="ml-1 italic text-gray-500">(**********)</span>
                </div>
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Date Founded:</div>
                  <span>January 1, 2025</span>
                </div>
              </div>
              <div class="flex flex-row w-full">
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Business Email:</div>
                  <span><EMAIL></span>
                </div>
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Business Number:</div>
                  <span>-</span>
                </div>
              </div>

              <fieldset class="fieldset w-full mt-4">
                <legend class="fieldset-legend">Business Status</legend>
                <select class="select">
                  <option disabled selected>Active</option>
                  <option>Suspended</option>
                  <option>Disabled</option>
                  <option>-</option>
                </select>
              </fieldset>

              <div class="flex flex-row w-full justify-between">
                <div class="flex gap-4">
                  <button
                    class="btn mt-4"
                    hx-get="components/modal-id-verif.html"
                    hx-trigger="click"
                    hx-target="#modal"
                    hx-swap="innerHTML"
                  >
                    Review ID Verification
                  </button>
                  <button class="btn mt-4">Past Verifications (3)</button>
                </div>
                <button class="btn mt-4">View All Members (37)</button>
              </div>
            </div>
          </div>

          <!-- Tenant + Payments-->
          <div class="flex flex-col lg:flex-row w-full mt-8 gap-8">
            <!-- Tenants-->
            <div class="w-full lg:w-1/2">
              <div
                class="w-full inline-flex justify-between mb-2 items-baseline"
              >
                <h2 class="text-lg font-bold w-full mb-4">Recent Properties</h2>
                <button
                  hx-get="components/modal-add-tenant.html"
                  hx-target="#modal"
                  hx-swap="innerHTML"
                  class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap"
                >
                  View All
                </button>
              </div>
              <div class="w-full">
                <!-- Properties list-->
                <table
                  class="w-full rounded-md overflow-hidden text-xs"
                  style="box-shadow: 0 0 0 1px rgb(203 213 225)"
                >
                  <tr
                    class="bg-slate-200 text-black uppercase border-b border-slate-300"
                  >
                    <th class="p-4 w-1/4 rounded-tl-md">
                      <span class="flex gap-2"> Date Created </span>
                    </th>
                    <th class="p-4 w-1/3">
                      <span class="flex gap-2"> Property Name </span>
                    </th>
                    <th class="p-4 w-1/3">
                      <span class="flex"> Address </span>
                    </th>
                  </tr>
                  <tr
                    class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t border-slate-300p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 2, 2025</span>
                    </td>
                    <td class="p-4 max-w-[149px]">
                      <div class="max-w-full overflow-auto whitespace-nowrap">
                        The House
                      </div>
                    </td>
                    <td class="p-4 max-w-[149px]">
                      <div class="max-w-full overflow-auto whitespace-nowrap">
                        1111 Countrystone Drive, unit 16A
                      </div>
                    </td>
                  </tr>
                  <tr
                    class="bg-slate-50 hover:bg-slate-100 text-left group cursor-pointer border-t border-slate-300 border-slate-300"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 1, 2025</span>
                    </td>
                    <td class="p-4 max-w-[149px]">
                      <div class="max-w-full overflow-auto whitespace-nowrap">
                        The Aaprtment
                      </div>
                    </td>
                    <td class="p-4 max-w-[149px]">
                      <div class="max-w-full overflow-auto whitespace-nowrap">
                        1111 Countrystone Drive, unit 16A
                      </div>
                    </td>
                  </tr>
                </table>
              </div>
            </div>

            <!-- Payments-->
            <div class="flex flex-wrap w-full lg:w-1/2 max-h-fit">
              <!-- Header-->
              <div
                class="w-full inline-flex justify-between mb-2 items-baseline"
              >
                <h2 class="text-lg font-bold w-full mb-4">Recent Leases</h2>
                <button
                  class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap"
                >
                  View All
                </button>
              </div>
              <div class="w-full">
                <table
                  class="w-full rounded-md overflow-hidden text-xs shadow-xl"
                  style="box-shadow: 0 0 0 1px rgb(203 213 225)"
                >
                  <tr
                    class="bg-slate-200 text-black uppercase border-b border-slate-300"
                  >
                    <th class="p-4 w-1/4 rounded-tl-md text-left">
                      Date Created
                    </th>
                    <th class="p-4 text-left">Primary Tenant</th>
                    <th class="p-4 text-left">
                      <span> Building </span>
                    </th>
                    <th class="p-4 text-left">
                      <span> Unit </span>
                    </th>
                  </tr>
                  <tr
                    class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t border-slate-300"
                    hx-get="components/modal-add-payment.html"
                    hx-target="#modal"
                    hx-swap="innerHTML"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 12, 2025</span>
                    </td>
                    <td class="p-4">Jane Doe</td>
                    <td class="p-4">The Apartment</td>
                    <td class="p-4">909</td>
                  </tr>
                  <tr
                    class="bg-slate-50 hover:bg-slate-100 text-left group cursor-pointer border-t border-slate-300"
                    hx-get="components/modal-add-payment.html"
                    hx-target="#modal"
                    hx-swap="innerHTML"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 12, 2025</span>
                    </td>
                    <td class="p-4">Joe Doe</td>
                    <td class="p-4">The House</td>
                    <td class="p-4">Mainfloor</td>
                  </tr>
                </table>
              </div>
            </div>
          </div>

          <!-- Delete-->
          <div class="w-full mt-8 mb-4">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              Delete User
            </h2>
            <button
              class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap mb-32"
              hx-get="components/modal-small.html"
              hx-target="#modal"
              hx-swap="innerHTML"
            >
              Delete User
            </button>
          </div>
        </div>
      </main>
    </div>

    <datalist id="test">
      <option value="Property 1"></option>
      <option value="Property 2"></option>
      <option value="Property 3"></option>
      <option value="Property 4"></option>
      <option value="Property 5"></option>
      <option value="Property 6"></option>
    </datalist>

    <!-- Modal Container -->
    <div id="modal" class=""></div>
  </body>
</html>