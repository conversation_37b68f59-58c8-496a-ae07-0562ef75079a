<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pricing</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Raleway:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link href="../output.css" rel="stylesheet" />
    <style>
      body {
        font-family: "Raleway", sans-serif;
      }
    </style>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <!-- Font is already imported in CSS -->

    <script>
      //swaps pricing cards between annual and monthly pricing
      function togglePricing(type) {
        const monthlyBtn = document.getElementById("monthly-btn")
        const annualBtn = document.getElementById("annual-btn")
        const priceList = document.getElementById("pricing-plan")

        if (type === "monthly") {
          monthlyBtn.classList.add("bg-white", "shadow-sm", "text-gray-900")
          monthlyBtn.classList.remove("text-gray-600")
          annualBtn.classList.remove("bg-white", "shadow-sm", "text-gray-900")
          annualBtn.classList.add("text-gray-600")

          priceList.setAttribute("hx-get", "./components/pricing-monthly.html")
        } else {
          annualBtn.classList.add("bg-white", "shadow-sm", "text-gray-900")
          annualBtn.classList.remove("text-gray-600")
          monthlyBtn.classList.remove("bg-white", "shadow-sm", "text-gray-900")
          monthlyBtn.classList.add("text-gray-600")

          priceList.setAttribute("hx-get", "./components/pricing-annual.html")
        }
        // Trigger the HTMX request
        htmx.process(priceList)
        htmx.trigger(priceList, "htmx:afterSettle")
      }

      // Toggles visibility of compare all features table
      var toggled = false

      function toggleFeatures() {
        const featureComp = document.getElementById("feature-comp")
        const grid = document.getElementById("feat-comp-grid")

        if (
          featureComp.style.maxHeight === "0px" ||
          !featureComp.style.maxHeight
        ) {
          featureComp.style.maxHeight = grid.scrollHeight + "px"
        } else {
          featureComp.style.maxHeight = "0px"
        }
      }

      // takes in an FAQ object and toggles the content and rotates the chevron
      function toggleFAQ(elem) {
        const parents = elem.parentNode
        const contentBox = parents.childNodes

        contentBox[3].classList.toggle("hidden")
        contentBox[1]
          .getElementsByTagName("img")[0]
          .classList.toggle("rotate-180")
      }

      document.addEventListener("DOMContentLoaded", styleTable)
    </script>
  </head>
  <body>
    <!-- NAV BAR-->
    <div
      class="sticky top-0 z-10"
      hx-get="./components/navbar.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>

    <!-- Main content-->
    <main
      class="flex flex-col items-center min-h-screen w-full bg-white px-4 lg:px-8"
    >
      <div class="flex flex-col w-full items-center mt-16 gap-8 max-w-[1024px]">
        <h4 class="text-4xl font-bold lg:text-left text-center">
          Make Rental Management Easy. Start free.
        </h4>
        <p class="text-lg lg:text-left text-center">
          Unlock a better way to manage your rentals. No credit card needed.
        </p>
        <div class="inline-flex p-1 bg-gray-100 rounded-full">
          <button
            id="monthly-btn"
            onclick="togglePricing('monthly')"
            class="px-6 py-2 rounded-full bg-white text-gray-900 font-medium shadow-sm transition-all duration-200"
          >
            Monthly
          </button>
          <button
            id="annual-btn"
            onclick="togglePricing('annual')"
            class="flex flex-col px-8 rounded-full text-gray-600 font-medium hover:text-gray-900 transition-all duration-200"
          >
            Annual
            <p class="text-xs">Save 20%</p>
          </button>
        </div>

        <!-- Pricing cards -->
        <div
          id="pricing-plan"
          hx-get="./components/pricing-monthly.html"
          hx-trigger="load"
          hx-swap="innerHTML"
        ></div>

        <!-- Feature Comparison -->
        <div
          class="flex flex-col w-full max-w-[992px] border border-gray-200 rounded-xl"
        >
          <div
            class="flex justify-between items-center w-full p-8 rounded-md text-2xl"
            onclick="toggleFeatures()"
          >
            <p>Compare all features</p>
            <img src="./assets/Plus.svg" />
          </div>
          <div
            id="feature-comp"
            class="max-h-0 overflow-hidden transition-[max-height] duration-700 ease-in-out"
          >
            <div
              id="feat-comp-grid"
              class="grid grid-cols-1 md:grid-cols-4 w-full"
            >
              <!-- Header Row - hide on mobile -->
              <div class="hidden md:block p-4"></div>
              <div class="hidden md:flex p-4 flex-col items-center">
                <p class="font-medium">Free</p>
                <a href="./signup.html" class="w-full">
                  <button
                    class="hidden md:block px-4 py-2 w-full bg-white border-1 border-rmred hover:bg-gray-50 text-rmred rounded-md text-base font-medium cursor-pointer transition-colors duration-100 mt-4"
                  >
                    Get Started
                  </button>
                </a>
              </div>
              <div class="hidden md:flex p-4 flex-col items-center">
                <p class="font-medium">Investor</p>
                <a href="./signup.html" class="w-full">
                  <button
                    class="hidden md:block px-4 py-2 w-full bg-rmred hover:bg-rmred-dark text-white rounded-md text-base font-medium cursor-pointer transition-colors duration-100 mt-4"
                  >
                    Get Started
                  </button>
                </a>
              </div>
              <div class="hidden md:flex p-4 flex-col items-center">
                <p class="font-medium">Property Manager</p>
                <a href="./signup.html" class="w-full">
                  <button
                    class="hidden md:block px-4 py-2 w-full bg-white border-1 border-rmred hover:bg-gray-50 text-rmred rounded-md text-base font-medium cursor-pointer transition-colors duration-100 mt-4"
                  >
                    Contact Us
                  </button>
                </a>
              </div>

              <!-- Feature Sections -->
              <div class="md:contents">
                <!-- Equifax Reporting -->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Rent reporting to Equifax
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    Pay per use
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    20 leases per month
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    Unlimited
                  </div>
                </div>
                <!-- Additional Reporting-->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Additional report cost
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    Pay per use
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    $1 per additional
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    Unlimited
                  </div>
                </div>

                <!-- Reporting Board Access -->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Reporting board access
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                </div>

                <!-- Unlimited lease tracking -->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Unlimited lease tracking
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Unlimited property tracking
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center justify-center lg:justify-start lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Team management
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                </div>

                <!-- Automated tenant notifs-->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Automated tenant notifications
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Priority customer support
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                </div>
                <!-- Setup Assist -->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Account setup assistance
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  API integration
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center lg:min-h-full justify-center lg:justify-start bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Custom team roles
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Customized branding
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="./assets/check-circle.svg" class="w-4" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- FAQ -->
        <div
          class="flex flex-col justify-center items-center w-full gap-16` mt-16 mb-16 max-w-[950px]"
        >
          <h3 class="font-bold text-3xl border-collapse">
            Frequently Asked Questions
          </h3>
          <!-- FAQ Wrapper -->
          <div
            class="flex flex-col w-full mt-16 [&_*]:border-gray-300 leading-relaxed"
          >
            <!-- FAQ question -->
            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-2xl w-full lg:w-4/5">
                  Is Report Rentals free to use?
                </h4>
                <img src="./assets/Chevron-down.svg".svg" />
              </div>
              <div div class="hidden w-full lg:w-4/5">
                <p>
                  Report Rentals has a free plan which is ideal for first time
                  users or individuals wanting to track a few leases. However,
                  for users managing more than 3 leases, upgrading to the
                  Investor plan is recommended.
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-2xl w-full lg:w-4/5">
                  How does Report Rentals' pricing work?
                </h4>
                <img src="./assets/Chevron-down.svg".svg" />
              </div>
              <div div class="hidden w-full lg:w-4/5">
                <p>
                  The free version of Report Rentals is available for individual
                  users. The Investor plan is priced at CAD $19.99 per user per
                  month with annual billing, or CAD $25.99 per user per month
                  with monthly billing. For organizations managing an extensive
                  portfolio, the Property Manager plan offers an
                  enterprise-level solution.
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-2xl w-full lg:w-4/5">
                  Do you offer any discounts?
                </h4>
                <img src="./assets/Chevron-down.svg".svg" />
              </div>
              <div div class="hidden w-full lg:w-4/5">
                <p>
                  Yes! By choosing a yearly plan, you will receive a 20%
                  discount.
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-2xl w-full lg:w-4/5">
                  How can I manage my billing?
                </h4>
                <img src="./assets/Chevron-down.svg".svg" />
              </div>
              <div div class="hidden w-full lg:w-4/5">
                <p>
                  You can manage your subscription and billing information
                  through
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-2xl w-full lg:w-4/5">
                  How would I update my plans?
                </h4>
                <img src="./assets/Chevron-down.svg".svg" />
              </div>
              <div div class="hidden w-full lg:w-4/5">
                <p>
                  You can manage your subscription and billing information at
                  any time by accessing your billing section in your account
                  settings. From there, click "change plan" to upgrade or
                  downgrade your plan.
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-2xl w-full lg:w-4/5">
                  Can my landlord send my information to Equifax without me
                  knowing?
                </h4>
                <img src="./assets/Chevron-down.svg".svg" />
              </div>
              <div div class="hidden w-full lg:w-4/5">
                <p>
                  No your landlord cannot begin sending rent reports to Equifax
                  withour your knowledge. In order to enable rent reporting,
                  tenants will receive a consent form to be digitally signed.
                </p>
                <br />
                <p>
                  Additionally, tenants will receive reminder emails for
                  upcoming and missed payments, as well as the opportunity to
                  dispute inaccurate information by messaging
                  <a
                    class="text-rmred font-medium"
                    href="mailto:<EMAIL>"
                    ><EMAIL></a
                  >.
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t border-b">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-2xl w-full lg:w-4/5">
                  I still have unanswered questions, what can I do?
                </h4>
                <img src="./assets/Chevron-down.svg".svg" />
              </div>
              <div div class="hidden w-full lg:w-4/5">
                <p>
                  We will do our best to help, please contact us with your
                  questions at
                  <a
                    class="text-rmred font-medium"
                    href="mailto:<EMAIL>"
                    ><EMAIL></a
                  >
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!--Footer-->
    <div
      hx-get="./components/footer.html"
      hx-trigger="load"
      hx-swap="innerHTML"
    ></div>
  </body>
</html>
