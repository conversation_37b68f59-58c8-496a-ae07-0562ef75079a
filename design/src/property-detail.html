<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Property</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
  </head>
  <body class="min-h-screen bg-gray-200">
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>

      <main class="w-full overflow-y-auto lg:px-16 md:px-8 px-4 bg-slate-100">
        <!-- Sticky bar-->
        <div
          class="fixed bottom-0 lg:ml-64 right-0 bg-white border lg:w-[calc(100%-16rem)] w-full z-50"
        >
          <div class="flex justify-between py-4 px-2 md:px-8">
            <button class="bg-white border text-black px-4 py-2 rounded">
              Download Record
            </button>
            <button class="bg-slate-800 text-white px-4 py-2 rounded">
              Save
            </button>
          </div>
        </div>

        <!-- Page Content-->
        <div class="max-w-[1440px] w-full mx-auto md:mt-24 lg:mt-4">
          <div class="w-full mt-20 md:mt-8 pb-0">
            <a class="text-sm underline" href="./properties.html"
              >← Back to properties</a
            >
          </div>

          <div class="w-full mt-8 pb-0">
            <h1 class="text-2xl">Property</h1>
          </div>

          <!-- Property Details-->
          <div
            class="flex flex-col lg:flex-row bg-white rounded border mt-4 w-full p-2 md:p-6 text-sm"
          >
            <!-- Property Details Section -->
            <div class="flex flex-1 flex-wrap">
              <h2 class="text-lg font-bold w-full mb-4">Property Details</h2>

              <label class="mb-2" for="property-name" label="Property Name"
                >Property Name</label
              >
              <input
                class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                id="property-name"
                placeholder="Create property name"
                value="Display name of the property"
              />
              <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
                <div class="flex flex-wrap md:w-1/2 w-full">
                  <label class="w-full mb-2" for="unit">Address</label>
                  <input
                    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                    id="unit"
                    placeholder="Default Unit"
                    value="1234 Oakville Road"
                  />
                </div>
                <div class="flex flex-wrap md:w-1/2 w-full">
                  <label class="w-full mb-2" for="unit">Unit</label>
                  <input
                    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                    id="unit"
                    placeholder=""
                    value=""
                  />
                </div>
              </div>
              <div class="w-full flex gap-4 mt-4">
                <div class="flex flex-wrap w-full">
                  <label class="w-full mb-2" for="unit">City/Town</label>
                  <input
                    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                    id="unit"
                    placeholder=""
                    value="Mississauga"
                  />
                </div>
              </div>
              <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
                <div class="flex flex-wrap md:w-1/3 w-full">
                  <label class="w-full mb-2" for="unit">Country</label>
                  <input
                    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                    id="unit"
                    list="test"
                    placeholder=""
                    value="Canada"
                  />
                </div>
                <div class="flex flex-wrap md:w-1/3 w-full">
                  <label class="w-full mb-2" for="unit">Province</label>
                  <input
                    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                    id="unit"
                    list="test"
                    placeholder=""
                    value="Ontario"
                  />
                </div>
                <div class="flex flex-wrap md:w-1/3 w-full">
                  <label class="w-full mb-2" for="unit">Postal Code</label>
                  <input
                    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                    id="unit"
                    placeholder=""
                    value="x1x 1x1"
                  />
                </div>
              </div>
              <div class="flex flex-wrap w-full mt-4">
                <label class="w-full mb-2" for="lease-status"
                  >Assigned To</label
                >
                <select
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none appearance-none cursor-pointer"
                  id="lease-status"
                  style="
                    background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M19 9l-7 7-7-7\'%3E%3C/path%3E%3C/svg%3E');
                    background-position: right 8px center;
                    background-repeat: no-repeat;
                    background-size: 20px;
                  "
                >
                  <option value="">All Agents (Default)</option>
                  <option value="Agent 1">Agent 1</option>
                  <option value="Agent 2">Agent 2</option>
                  <option value="Agent 3">Agent 3</option>
                </select>
              </div>
            </div>

            <!-- Documents + Options Section -->
            <div
              class="flex flex-wrap flex-1 lg:ml-8 mt-8 lg:mt-0 w-full h-full"
            >
              <div
                class="flex flex-col bg-gray-200 p-4 rounded w-full mb-4 gap-4"
              >
                <span class="font-bold mb-2">Property Document</span>
                <div
                  class="py-8 flex flex-col items-center justify-center w-full border-2 border-dashed rounded-md border-gray-500 gap-2"
                >
                  <!-- File Input -->
                  <div id="file-input" class="flex flex-col items-center gap-2">
                    <label
                      class="bg-slate-800 text-white px-4 py-2 rounded-md hover:cursor-pointer"
                    >
                      Choose File
                      <input
                        class="hidden"
                        type="file"
                        onchange="
                        document.getElementById('file-input').classList.add('hidden');
                        document.getElementById('file-preview').classList.remove('hidden');
                        document.getElementById('file-name').textContent = this.files[0]?.name || 'No file chosen';
                        document.getElementById('file-link').href = URL.createObjectURL(this.files[0]);"
                      />
                    </label>
                    <span class="text-gray-500 text-sm">No file chosen</span>
                  </div>

                  <!-- File Preview -->
                  <div
                    id="file-preview"
                    class="hidden flex flex-col items-center gap-3"
                  >
                    <a
                      id="file-link"
                      href="#"
                      target="_blank"
                      class="flex items-center gap-2 hover:bg-gray-100 bg-gray-50 px-3 py-1.5 rounded-md text-gray-700"
                    >
                      <svg
                        class="w-4 h-4 text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                        />
                      </svg>
                      <span id="file-name"></span>
                    </a>
                    <button
                      class="text-sm text-red-600 hover:text-red-800"
                      onclick="
                      document.getElementById('file-preview').classList.add('hidden');
                      document.getElementById('file-input').classList.remove('hidden');
                      document.querySelector('input[type=file]').value = '';
                      URL.revokeObjectURL(document.getElementById('file-link').href);"
                    >
                      Remove file
                    </button>
                  </div>
                </div>
              </div>
              <div class="flex bg-gray-200 p-4 rounded w-full flex-wrap">
                <span class="font-bold w-full">Options</span>
                <div class="flex flex-col gap-4 py-4 w-full">
                  <!-- First toggle -->
                  <div class="flex items-center justify-between w-full">
                    <label
                      for="rent-reporting"
                      class="flex items-center gap-2 font-medium text-gray-700 w-full"
                    >
                      Option1
                      <div class="group relative inline-block">
                        <img
                          src="assets/Help-circle.svg"
                          alt=""
                          class="hover:cursor-help"
                        />
                        <span
                          class="invisible group-hover:visible opacity-0 group-hover:opacity-100 border border-gray-300 transition bg-white text-black text-sm rounded-md p-2 absolute z-50 -top-16 left-1/2 -translate-x-1/2 w-48 text-center"
                        >
                          Info about this option
                        </span>
                      </div>
                    </label>
                    <label
                      class="relative inline-flex items-center cursor-pointer"
                    >
                      <input type="checkbox" class="sr-only peer" />
                      <div
                        class="w-11 h-6 bg-gray-100 border border-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                      ></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Tenant + Payments-->
          <div class="flex flex-col lg:flex-row w-full mt-8 gap-8">
            <!-- Tenants-->
            <div class="w-full">
              <div
                class="w-full inline-flex justify-between mb-2 items-baseline"
              >
                <h2 class="text-lg font-bold w-full mb-4">Rooms</h2>
                <button
                  hx-get="components/modal-add-room.html"
                  hx-target="#modal"
                  hx-swap="innerHTML"
                  class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap"
                >
                  Create Room
                </button>
              </div>
              <!-- Room list-->
              <table
                class="w-full rounded-md overflow-hidden"
                style="box-shadow: 0 0 0 1px rgb(203 213 225)"
              >
                <tr class="bg-slate-200 text-left text-xs text-black uppercase">
                  <th class="px-2 py-4 md:p-4 w-4/12">
                    <span class="flex items-center gap-2"> Name</span>
                  </th>
                  <th class="px-2 py-4 md:p-4">
                    <span class="flex items-center gap-2"> Leaseholder</span>
                  </th>
                  <th class="px-2 py-4 md:p-4">
                    <span class="flex items-center gap-2"> Owing Balance </span>
                  </th>
                  <th class="px-2 py-4 md:p-4">
                    <span class="flex items-center justify-center"> Edit </span>
                  </th>
                </tr>
                <tr
                  class="bg-white hover:bg-slate-50 text-left text-sm cursor-pointer"
                  hx-get="components/modal-add-room.html"
                  hx-trigger="click"
                  hx-target="#modal"
                  hx-swap="innerHTML"
                >
                  <td class="px-2 py-4 md:p-4">
                    <span class="flex items-center gap-2">Mainfloor</span>
                  </td>
                  <td class="px-2 py-4 md:p-4">
                    <a href="lease-detail.html">John Smith</a>
                  </td>
                  <td class="px-2 py-4 md:p-4">$1000</td>
                  <td>
                    <img src="./assets/more-vertical.svg" class="m-auto" />
                  </td>
                </tr>
              </table>
            </div>
          </div>

          <!-- Notes-->
          <div class="w-full mt-8">
            <label class="w-full" for="unit" required>
              <h2 class="text-lg font-semibold text-gray-900">
                Property Notes
              </h2>
              <p class="text-sm text-gray-500 mb-4">
                A place to keep notes for internal purposes. Visible to only you
                and your team.
              </p>
            </label>
            <textarea
              class="p-4 w-full border border-gray-300 rounded-md focus:outline-none h-48 resize-none"
              id="unit"
              placeholder="Add notes about the property here"
            ></textarea>
          </div>
          <!-- Delete-->
          <div class="w-full mt-8 mb-4">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              Delete Property
            </h2>
            <button
              class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap mb-32"
              hx-get="components/modal-small.html"
              hx-target="#modal"
              hx-swap="innerHTML"
            >
              Delete Property
            </button>
          </div>
        </div>
      </main>
    </div>

    <datalist id="test">
      <option value="Property 1"></option>
      <option value="Property 2"></option>
      <option value="Property 3"></option>
      <option value="Property 4"></option>
      <option value="Property 5"></option>
      <option value="Property 6"></option>
    </datalist>

    <!-- Modal Container -->
    <div id="modal" class=""></div>
  </body>
</html>
