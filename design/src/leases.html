<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Leases</title>
    <link href="output.css" rel="stylesheet" />
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
  </head>
  <body class="min-h-screen">
    <div class="flex">
      <!-- Nav bar-->
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>
      <!-- Right Content Box -->
      <main class="h-screen w-full bg-slate-100 overflow-auto lg:px-16 md:px-8 px-4">
        <div class="max-w-[1440px] w-full mx-auto mb-32">
          <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
            <h1 class="text-2xl">My Leases</h1>
            <!-- Search Bar -->
            <div class="mt-8 w-full">
              <div class="flex gap-2">
                <label class="input">
                  <svg class="h-[1em] opacity-50" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g
                      stroke-linejoin="round"
                      stroke-linecap="round"
                      stroke-width="2.5"
                      fill="none"
                      stroke="currentColor"
                    >
                      <circle cx="11" cy="11" r="8"></circle>
                      <path d="m21 21-4.3-4.3"></path>
                    </g>
                  </svg>
                  <input type="search" class="grow" placeholder="Search for a property or name" />
                </label>
                <button
                  class="hidden sm:block bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm whitespace-nowrap"
                >
                  Search
                </button>
              </div>
            </div>

            <!-- Filter and Create Section -->
            <div
              class="flex flex-col justify-between sm:items-center sm:flex-row w-full gap-4 sm:mt-8 mt-4"
            >
              <!-- Filter By -->
              <div class="flex flex-row gap-4 items-center">
                <span class="text-sm text-gray-500 whitespace-nowrap">Filter By</span>
                
                  <select class="select">
                  <option selected>Current Leases</option>
                  <option>Past Leases</option>
                  <option>Show All</option>
                </select>
              </div>

              <!-- Create Lease Button -->
              <div class="w-fit flex justify-end">
                <button
                  class="flex items-center whitespace-nowrap bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 gap-2 rounded text-sm"
                  hx-get="components/modal-create-lease.html"
                  hx-target="#modal"
                  hx-swap="innerHTML"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Create Lease
                </button>
              </div>
            </div>
          </div>

      <!-- Desktop View -->
      <table class="hidden lg:table w-full text-sm rounded-md overflow-hidden mt-4"
             style="box-shadow: 0 0 0 1px rgb(203 213 225)">
        <thead>
            <tr class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300">
                <th class="p-4 w-3/12">
                    <span class="flex items-center gap-2">
                        Name
                        <img src="assets/Selector.svg" class="w-4 h-4" alt="">
                    </span>
                </th>
                <th class="p-4">
                    <span class="flex items-center gap-2">
                        Room
                        <img src="assets/Selector.svg" class="w-4 h-4" alt="">
                    </span>
                </th>
                <th class="p-4">
                    <span class="flex items-center gap-2">
                        Last Payment
                        <img src="assets/Selector.svg" class="w-4 h-4" alt="">
                    </span>
                </th>
                <th class="p-4">
                    <span class="flex items-center gap-2">
                        Rent Report
                        <img src="assets/Selector.svg" class="w-4 h-4" alt="">
                    </span>
                </th>
                <th class="p-4">
                    <span class="flex items-center gap-2">
                        Owing Balance
                        <img src="assets/Selector.svg" class="w-4 h-4" alt="">
                    </span>
                </th>
            </tr>
        </thead>
        <tr class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t" onclick="toggleContent(this)">
          <td class="p-4">
            <span class="flex items-center gap-2 font-medium w-full truncate">
                <svg class="w-5 h-5 text-gray-500 transform flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
                <span class="truncate">1234 propertyname</span>
            </span>
          </td>
          <td class="p-4"></td>
          <td class="p-4"></td><td class="p-4"></td>
          <td class="p-4">$0</td>
        </tr>
        <!-- First Tenant -->
        <tr class="hidden bg-white hover:bg-slate-50 text-left">
          <td class="p-4 pl-10">
            <a href="./lease-detail.html" class="flex items-center gap-2">
              <span class="flex items-center gap-2">
                <svg class="w-5 h-5 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                John Smith
              </span>
            </a>
          </td>
          <td class="p-4">test</td>
          <td class="p-4">test</td>
          <td class="p-4">On</td>
          <td class="p-4">test</td>
        </tr>
        <!-- Second Tenant -->
        <tr class="hidden bg-white hover:bg-slate-50 text-left text-gray-500">
          <td class="p-4 pl-10">
            <a href="./lease-detail.html">
              <span class="flex items-center gap-2">
                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path 
                      stroke-linecap="round" 
                      stroke-linejoin="round" 
                      stroke-width="2" 
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8V7a4 4 0 00-8 0v4"
                  />
                </svg>
                Jane Doe
              </span>
            </a>
          </td>
          <td class="p-4">test</td>
          <td class="p-4">test</td>
          <td class="p-4">Off</td>
          <td class="p-4">test</td>
        </tr>
      </table>

       <!-- Desktop Pagination -->
       <div class="hidden lg:flex justify-between items-center gap-4 px-4 py-2 mt-4 bg-white border border-gray-300 rounded-lg">
         <span class="text-sm text-gray-500">Showing 3 of 3 entries</span>
         <div class="join">
          <button class="join-item btn">1</button>
          <button class="join-item btn btn-active">2</button>
          <button class="join-item btn">3</button>
          <button class="join-item btn">4</button>
        </div>
       </div>


      <!-- Mobile/Tablet View -->
      <div class="lg:hidden mt-8">
        <!-- Property Group -->
        <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
            <div class="flex flex-wrap p-4 bg-slate-200 cursor-pointer group" onclick="toggleContent(this)">
                <span class="flex items-center gap-2 font-medium w-full truncate">
                    <svg class="w-5 h-5 text-gray-500 transform flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                    <span class="truncate">1234 propertyname</span>
                </span>
            </div>
            <!-- Lease Details (visible by default) -->
            <div class="flex flex-wrap p-4 bg-white border-t">
              <div class="flex items-center gap-2 mb-2">
                <img src="assets/user.svg" class="w-5 h-5 text-gray-500" alt="">
                <span class="font-medium w-full">John Smith</span>
              </div>
                <div class="flex justify-between w-full">
                    <span>Room: 101</span>
                    <span>Balance: $100</span>
                </div>
                <div class="flex justify-between w-full">
                  <span>Rent Report: On</span>
                  <span>Last Payment: Jan 1</span>
              </div>
            </div>
        </div>
      </div>
    </div>
  </div>
    </main>

    <!-- Modal Container -->
    <div id="modal" class=""></div>

    <script>
    function toggleContent(element, stopClass = 'font-bold') {
      let current = element.nextElementSibling;
      // If stopClass is provided, toggle multiple elements until stopClass is found
      if (stopClass) {
        while (current && !current.classList.contains(stopClass)) {
          current.classList.toggle('hidden');
          current = current.nextElementSibling;
        }
      } else {
        // Otherwise just toggle the next element
        current.classList.toggle('hidden');
      }
      const arrow = element.querySelector('svg');
      arrow.classList.toggle('rotate-90');
    }
    </script>
  </body>
</html>
