<div class="fixed inset-0 z-50">
  <!-- Dark overlay -->
  <div
    class="fixed inset-0 bg-black/50 pointer-events-auto"
    onclick="this.parentElement.remove()"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl max-w-[1120px] sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">View Report</h3>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          onclick="document.getElementById('modal').innerHTML = ''"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-4 lg:px-16 py-4 overflow-auto h-full">
        <!-- Report Details -->
        <div>
          <div class="block">
            <div class="flex items-center gap-2">
              <p class="text-sm text-black">Firstname Lastname</p>
              <p class="text-sm text-gray-500">Posted August 22, 2024</p>
            </div>
            <h1 class="text-lg font-semibold text-gray-900 mt-4">
              Destruction of property - 123 oakstreet avenue, Mainfloor
            </h1>

            <p class="mt-2 text-sm">
              John Smith (Tenant) and Landlording Company Inc (Landlord)
            </p>
          </div>
          <div class="mt-4 block">
            <p class="mt-2 text-sm">
              Note 123 by the landlord etc, probably give it a character limit
            </p>
            <h2 class="font-semibold mt-4">Documents</h2>
            <div class="flex gap-2 mt-4">
              <img src="" class="bg-gray-200 rounded-md w-32 h-32" />
              <img src="" class="bg-gray-200 rounded-md w-32 h-32" />
            </div>
          </div>
          <div class="flex justify-start mt-8">
            <div class="flex w-fit rounded p-2 text-xs gap-6">
              <div class="flex gap-2 items-center min-h-11">
                <img src="assets/thumbsup.svg" class="w-5 h-5" alt="" />
                <span>3</span>
              </div>
              <div class="flex gap-2 items-center min-h-11">
                <img src="assets/Link.svg" class="w-5 h-5" alt="" />
                <span>share</span>
              </div>
              <div class="flex gap-2 items-center min-h-11">
                <img src="assets/more-vertical.svg" class="w-5 h-5" alt="" />
              </div>
            </div>
          </div>
        </div>
        <hr class="mt-8" />
        <!-- Comments -->
        <div class="w-full mt-8">
          <div class="relative">
            <textarea
              class="p-4 w-full border border-gray-300 rounded-t-md focus:outline-none h-16 resize-none transition-all duration-200 focus:h-32"
              id="unit"
              placeholder="Make a comment"
              onclick="this.nextElementSibling.classList.remove('hidden')"
            ></textarea>

            <!-- Utility Bar -->
            <div
              class="hidden w-full border border-t-0 border-gray-300 rounded-b-md p-2 bg-white"
            >
              <div class="flex justify-between items-center">
                <!-- Text Markup Icons -->
                <div class="flex gap-3 items-center">
                  <button>
                    <img src="assets/placeholder.svg" class="w-4 h-4" alt="" />
                  </button>
                  <button>
                    <img src="assets/placeholder.svg" class="w-4 h-4" alt="" />
                  </button>
                  <button>
                    <img src="assets/placeholder.svg" class="w-4 h-4" alt="" />
                  </button>
                </div>
                <button
                  class="bg-slate-800 hover:bg-slate-900 text-white px-4 py-1.5 rounded text-sm transition-colors"
                >
                  Post
                </button>
              </div>
            </div>
          </div>
        </div>
        <!-- Comments container -->
        <div class="w-full mt-8 mb-32 flex flex-col gap-8">
          <!-- Comment 1 -->
          <div class="relative bg-white border rounded-md p-4">
            <p class="text-sm text-gray-500">
              Newguy Lastname - Posted August 22, 2024
            </p>
            <p class="mt-2 text-sm">
              John Smith (Tenant) and Landlording Company Inc (Landlord)
            </p>
            <div class="flex justify-start mt-8">
              <div class="flex w-fit rounded p-2 text-xs gap-6">
                <div class="flex gap-2 items-center">
                  <img src="assets/thumbsup.svg" class="w-5 h-5" alt="" />
                  <span>3</span>
                </div>
                <div class="flex gap-2 items-center">
                  <img src="assets/message.svg" class="w-5 h-5" alt="" />
                  <span>reply</span>
                </div>
                <div class="flex gap-2 items-center">
                  <img src="assets/more-vertical.svg" class="w-5 h-5" alt="" />
                </div>
              </div>
            </div>
          </div>
          <div class="relative bg-white border rounded-md p-4">
            <p class="text-sm text-gray-500">
              Newguy Lastname - Posted August 22, 2024
            </p>
            <p class="mt-2 text-sm">
              John Smith (Tenant) and Landlording Company Inc (Landlord)
            </p>
            <div class="flex justify-start mt-8">
              <div class="flex w-fit rounded p-2 text-xs gap-6">
                <div class="flex gap-2 items-center">
                  <img src="assets/thumbsup.svg" class="w-5 h-5" alt="" />
                  <span>3</span>
                </div>
                <div class="flex gap-2 items-center">
                  <img src="assets/message.svg" class="w-5 h-5" alt="" />
                  <span>reply</span>
                </div>
                <div class="flex gap-2 items-center">
                  <img src="assets/more-vertical.svg" class="w-5 h-5" alt="" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
