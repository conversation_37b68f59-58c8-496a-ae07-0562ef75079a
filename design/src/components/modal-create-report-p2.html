<!-- This is the next page of @modal-create-report.html-->
<div class="fixed inset-0 z-50">
  <!-- Dark overlay -->
  <div
    class="fixed inset-0 bg-black/50 pointer-events-auto"
    onclick="this.parentElement.remove()"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl max-w-[1120px] sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Create Report</h3>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          onclick="document.getElementById('modal').innerHTML = ''"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-8 py-6 overflow-y-auto h-[calc(100%-8rem)]">
        <!-- Report Details -->
        <div class="flex flex-wrap w-full space-y-6 mb-16">
          <!-- Header Section -->
          <div class="w-full">
            <h2 class="text-lg font-bold">Report Details</h2>
            <p class="text-sm mt-2 text-gray-600">
              Creating a report for <b>John Smith</b> at
              <b>1234 Property Street Room 001, Mississauga, X0X 0X0</b>
            </p>
          </div>

          <!-- Reason Section -->
          <div class="w-full">
            <h3 class="text-base font-medium mb-3">Reason for report</h3>
            <select
              class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none appearance-none cursor-pointer"
              style="
                background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M19 9l-7 7-7-7\'%3E%3C/path%3E%3C/svg%3E');
                background-position: right 8px center;
                background-repeat: no-repeat;
                background-size: 20px;
              "
            >
              <option value="">Select a reason...</option>
              <option value="late_payment">Late Payment</option>
              <option value="damage">Property Damage</option>
              <option value="noise">Noise Complaint</option>
              <option value="other">Other</option>
            </select>
          </div>

          <!-- Visibility Section -->
          <div class="w-full">
            <h3 class="text-base font-medium mb-3">Report Visibility</h3>
            <div class="flex gap-6">
              <label class="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="visibility"
                  value="private"
                  checked
                  class="w-4 h-4 text-slate-800 border-gray-300 focus:ring-slate-800"
                />
                <span class="text-sm">Private</span>
              </label>

              <label class="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="visibility"
                  value="public"
                  class="w-4 h-4 text-slate-800 border-gray-300 focus:ring-slate-800"
                />
                <span class="text-sm">Public</span>
              </label>
            </div>
          </div>

          <!-- Details Section -->
          <div class="w-full">
            <div class="flex justify-between items-center mb-3">
              <h3 class="text-base font-medium">Report Details</h3>
              <div class="relative">
                <input
                  type="file"
                  id="fileUpload"
                  class="hidden"
                  multiple
                  accept=".pdf,.doc,.docx,.txt"
                />
                <button
                  class="flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 transition-colors"
                  onclick="document.getElementById('fileUpload').click()"
                >
                  + Upload Files
                </button>
              </div>
            </div>
            <textarea
              class="p-4 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-slate-800 focus:border-slate-800 h-40 resize-none"
              placeholder="Write your report details here..."
            ></textarea>

            <!-- Uploaded Files List -->
            <div class="mt-3 p-3 bg-gray-50 rounded-md">
              <div
                class="flex items-center justify-between py-2 border-b border-gray-200 last:border-0"
              >
                <span class="text-sm text-gray-700">document1.pdf</span>
                <button class="text-gray-500 hover:text-gray-700">×</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Modal Footer -->
      <div
        class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          hx-get=""
          hx-target="#modal"
          hx-swap="innerHTML"
        >
          Back
        </button>
        <button
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Post
        </button>
      </div>
    </div>
  </div>
</div>
