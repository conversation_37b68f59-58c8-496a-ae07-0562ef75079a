<div class="fixed inset-0 z-50">
    <!-- Dark overlay with click handler to close -->
    <div
      class="fixed inset-0 bg-black/50 pointer-events-auto"
      onclick="closeModal()"
    ></div>
  
    <!-- Modal container -->
    <div
      class="fixed inset-0 flex items-center justify-center pointer-events-none"
    >
      <div
        class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
      >
        <!-- Modal Header -->
        <div
          class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
        >
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Create Property</h3>
            <p>description here</p>
          </div>
          <button
            class="p-2 hover:bg-gray-100 rounded-full"
            hx-get=""
            hx-target="#modal"
            hx-swap="innerHTML"
          >
            <svg
              class="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
  
        <!-- Modal Body -->
        <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
          <div id="row wrapper" class="flex flex-col gap-4">
            Pretend this is the create property drawer
          </div>
        </div>
  
        <!-- Modal Footer -->
        <div
          class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t"
        >
          <button
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            hx-get="components/modal-create-lease.html"
            hx-target="#modal"
            hx-swap="innerHTML"
          >
            Cancel
          </button>
          <button
            class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
          >
            Create Property
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <datalist id="test">
    <option value="Property 1"></option>
    <option value="Property 2"></option>
    <option value="Property 3"></option>
    <option value="Property 4"></option>
    <option value="Property 5"></option>
    <option value="Property 6"></option>
  </datalist>