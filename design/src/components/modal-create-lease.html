<div class="fixed inset-0 z-50">
  <!-- Dark overlay with click handler to close -->
  <div
    class="fixed inset-0 bg-black/50 pointer-events-auto"
    onclick="closeModal()"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Create Lease</h3>
          <p>description here</p>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          hx-get=""
          hx-target="#modal"
          hx-swap="innerHTML"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
        <div id="row wrapper" class="flex flex-col gap-4">
          <!-- 1st Row-->
          <div class="flex flex-1 w-full gap-4">
            <div class="flex flex-wrap w-full">
              <label class="w-full mb-2" ">Property Name</label>
              <select class="select flex-1">
                <option disabled selected>Select a unit</option>
                <option>Create a unit</option>
                <option>property</option>
                <option>property</option>
                <option>property</option>
              </select>
            </div>
            <div class="flex flex-wrap w-full">
              <label class="w-full mb-2 text-sm">Property Name</label>
              <select class="select flex-1">
                <option disabled selected>Select a unit</option>
                <option>Create a unit</option>
                <option>property</option>
                <option>property</option>
                <option>property</option>
              </select>
            </div>
          </div>

          <!-- 2nd Row-->
          <div class="flex flex-row gap-4">
            <div class="flex flex-wrap flex-1 w-full">
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2 text-sm" "
                  >Move in Date</label
                >
                <label class="input">
                  <svg
                    class="h-[1em] opacity-50"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                    <line x1="16" y1="2" x2="16" y2="6" />
                    <line x1="8" y1="2" x2="8" y2="6" />
                    <line x1="3" y1="10" x2="21" y2="10" />
                  </svg>
                  <input type="search" class="grow" placeholder="Select a date" />
                </label>
              </div>
            </div>

            <div class="flex flex-wrap flex-1 w-full">
              <label class="w-full mb-2 text-sm" for="rent-due"
                >Monthly Rent</label
              ><label class="input">
                <span class="opacity-50">$</span>
                <input type="search" class="grow" placeholder="1" />
              </label>
            </div>

            <div class="flex flex-wrap flex-1 w-full">
              <label class="w-full mb-2 text-sm" for="rent-due"
                >Additional Monthly Fees</label
              ><label class="input">
                <span class="opacity-50">$</span>
                <input type="search" class="grow" placeholder="0" />
              </label>
            </div>
          </div>

          <div class="flex flex-row gap-4">
            <div class="flex flex-wrap flex-1 w-full">
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2 text-sm" "
                  >Key Deposit</label
                >
                <label class="input">
                  <span class="opacity-50">$</span>
                  <input type="search" class="grow" placeholder="0" />
                </label>
              </div>
            </div>

            <div class="flex flex-wrap flex-1 w-full">
              <label class="w-full mb-2 text-sm" for="rent-due"
                >Rent Deposit</label
              ><label class="input">
                <span class="opacity-50">$</span>
                <input type="search" class="grow" placeholder="0" />
              </label>
            </div>

            <div class="flex flex-wrap flex-1 w-full">
              <label class="w-full mb-2 text-sm" for="rent-due"
                >Rent Deposit</label
              ><label class="input">
                <span class="opacity-50">$</span>
                <input type="search" class="grow" placeholder="0" />
              </label>
            </div>
          </div>
        </div>
      </div>
      <!--
      Old Modal Body (if you delete this it breaks, idk why)
      <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
        <form>
          <div class="flex flex-wrap">
            <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
              <div class="flex flex-wrap w-full">
                <label >Property Name</label>
              </div>
            </div>
          </div>
        </form>
      </div> 
      -->

      <!-- Modal Footer -->
      <div
        class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          hx-get=""
          hx-target="#modal"
          hx-swap="innerHTML"
        >
          Cancel
        </button>
        <button
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Create Lease
        </button>
      </div>
    </div>
  </div>
</div>

<datalist id="test">
  <option value="Property 1"></option>
  <option value="Property 2"></option>
  <option value="Property 3"></option>
  <option value="Property 4"></option>
  <option value="Property 5"></option>
  <option value="Property 6"></option>
</datalist>
