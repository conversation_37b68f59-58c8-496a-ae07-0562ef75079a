<div class="fixed inset-0 z-50">
  <!-- Dark overlay -->
  <div
    class="fixed inset-0 bg-black/50 pointer-events-auto"
    onclick="this.parentElement.remove()"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl max-w-[1120px] sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Create Report</h3>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          onclick="document.getElementById('modal').innerHTML = ''"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-8 lg:px-16 py-4 overflow-auto h-full">
        <!-- Report Details -->
        <div class="flex flex-wrap mt-8 mb-4 w-full gap-2">
          <h2 class="text-lg font-bold w-full">Select Tenant</h2>
          <p class="text-sm">
            Find and select the tenant you would like to make a report for.
          </p>
          <!-- Search Bar-->
          <div class="flex justify-between items-center w-full mt-2">
            <div class="flex gap-2 w-full">
              <input
                placeholder="Search for a property or name"
                type="text"
                class="p-2 px-10 border border-1 rounded text-sm flex-1 w-full"
                style="
                  background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\'%3E%3C/path%3E%3C/svg%3E');
                  background-position: 8px 8px;
                  background-repeat: no-repeat;
                  background-size: 20px;
                "
              />
            </div>
          </div>
        </div>
      </div>
      <!-- Modal Footer -->
      <div
        class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          hx-get=""
          hx-target="#modal"
          hx-swap="innerHTML"
        >
          Cancel
        </button>
        <button
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</div>
