<div class="fixed inset-0 z-50">
    <!-- Dark overlay with click handler to close -->
    <div
      class="fixed inset-0 bg-black/50 pointer-events-auto"
      hx-get=""
      hx-target="#modal"
      hx-swap="innerHTML"
      hx-trigger="click"
    ></div>
  
    <!-- Modal container -->
    <div
      class="fixed inset-0 flex items-center justify-center pointer-events-none"
    >
      <div
        class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 max-w-[1120px] w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
      >
        <!-- Modal Header -->
        <div
          class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
        >
          <div>
            <h3 class="text-lg font-semibold text-gray-900">
              Review ID Verification
            </h3>
            <p class="text-sm text-gray-500">
              Make sure submitted information match
            </p>
          </div>
          <button
            class="p-2 hover:bg-gray-100 rounded-full"
            hx-get=""
            hx-target="#modal"
            hx-swap="innerHTML"
          >
            <svg
              class="w-5 h-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
  
        <!-- Modal Body -->
        <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
          <h3 class="text-lg font-semibold text-gray-900">Review Information</h3>
          <span>Lorem Ipsum</span>
          <div class="flex flex-row justify-between mt-8 gap-2">
            <div class="flex flex-row w-full">
              <div class="flex flex-col w-1/2 gap-4">
                <div class="flex flex-row">
                  <span class="font-bold w-36">Legal First Name:</span
                  ><span>John</span>
                </div>
  
                <div class="flex flex-row">
                  <span class="font-bold w-36">Legal Last Name:</span
                  ><span>Smith</span>
                </div>
  
                <div class="flex flex-row">
                  <span class="font-bold w-36">Gender:</span><span>Male</span>
                </div>
  
                <div class="flex flex-row">
                  <span class="font-bold w-36">Date of Birth:</span
                  ><span>January 1, 1999</span>
                </div>
  
                <div class="flex flex-row">
                  <span class="font-bold w-36">Address:</span>
                  <div class="flex flex-col">
                    <span>1234 Street road, Mississauga</span>
                    <span>Ontario, X0X 0X0, Canada</span>
                  </div>
                </div>
  
                <div class="flex flex-row">
                  <span class="font-bold w-36">Nationality:</span>
                  <span>Canadian</span>
                </div>
              </div>
              <div
                class="flex flex-row w-1/2 h-8 bg-gray-200 align-middle content-center gap-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-6"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
                  />
                </svg>
  
                <span>Document</span>
              </div>
            </div>
          </div>
  
          <div class="flex flex-row justify-between mt-8">
            <div class="w-1/2">
              <input type="radio" name="radio-1" class="radio mr-2" />
              <span>The above information matches and is correct</span>
            </div>
            <div class="w-1/2">
              <!-- If this is checked, hide the textbox-->
              <input
                id="Incorrect-ID"
                type="radio"
                name="radio-1"
                class="radio mr-2"
                checked="checked"
              />
              <span>
                The above information
                <span class="font-bold">does not</span> match and is
                <span class="font-bold">not correct</span>
              </span>
            </div>
          </div>
          <textarea
            class="textarea mt-8 mb-16 w-full h-64"
            placeholder="Reason for rejection"
          >
  The provided information do not match</textarea
          >
        </div>
  
        <!-- Modal Footer -->
        <div
          class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 rounded-b-lg flex justify-between gap-2 border-t"
        >
          <button
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            hx-get=""
            hx-target="#modal"
            hx-swap="innerHTML"
          >
            Cancel
          </button>
          <button
            class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
          >
            Complete Review
          </button>
        </div>
      </div>
    </div>
  </div>