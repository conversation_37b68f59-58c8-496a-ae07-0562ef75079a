<!-- Mobile Header Bar -->
<div
  class="lg:hidden fixed top-0 left-0 right-0 h-16 bg-white z-40 flex items-center justify-between px-4"
  style="box-shadow: 0 0 0 1px rgb(203 213 225)"
>
  <!-- Hamburger Button -->
  <button
    onclick="document.getElementById('mobile-menu').classList.toggle('hidden');
             document.getElementById('overlay').classList.toggle('hidden')"
    class="p-2 rounded-md hover:bg-gray-100"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M4 6h16M4 12h16M4 18h16"
      />
    </svg>
  </button>
  <div class="h-12 w-32"><img src="assets/logo-graphic.svg" alt="" /></div>
</div>

<!-- Navigation -->
<nav
  id="mobile-menu"
  class="lg:w-64 w-full bg-white text-black min-h-screen lg:relative fixed lg:top-0 z-40 hidden lg:block h-[calc(100vh-4rem)] shadow-[1px_0_0px_0_rgba(0,0,0,0.05)] overflow-hidden"
>
  <ul class="text-base">
    <li class="lg:block hidden">
      <!-- Desktop Logo -->
      <a href="./home.html" class="block p-4">
        <img src="assets/logo-graphic.svg" alt="" class="w-36" />
      </a>
    </li>
    <li class="lg:hidden">
      <div class="flex justify-between items-center px-4 h-64">
        <button
          class="hover:bg-slate-200 transition p-2 rounded"
          onclick="document.getElementById('mobile-menu').classList.add('hidden');
                   document.getElementById('overlay').classList.add('hidden')"
        >
          <img src="assets/close.svg" alt="" />
        </button>
        <img src="assets/logo-graphic.svg" alt="Logo" class="h-12" />
      </div>
    </li>
    <li>
      <a
        href="#"
        class="block px-5 py-6 hover:bg-slate-100 hover:text-slate-900 transition-all"
      >
        <span class="flex items-center gap-6">
          <img src="assets/mail.svg" alt="" />Inbox
        </span>
      </a>
    </li>

    <li>
      <a
        href="../src/leases.html"
        class="block px-5 py-6 hover:bg-slate-100 hover:text-slate-900 transition-all"
      >
        <span class="flex items-center gap-6">
          <img src="assets/file.svg" alt="" />My Leases
        </span>
      </a>
    </li>
    <li>
      <a
        href="../src/forum.html"
        class="block px-5 py-6 hover:bg-slate-100 hover:text-slate-900 transition-all"
      >
        <span class="flex items-center gap-6">
          <img src="assets/message.svg" alt="" />Forum
        </span>
      </a>
    </li>
    <li>
      <a
        href="#"
        class="block px-5 py-6 hover:bg-slate-100 hover:text-slate-900 transition-all"
      >
        <span class="flex items-center gap-6">
          <img src="assets/help-circle.svg" alt="" />Report Problem
        </span>
      </a>
    </li>
    <li class="lg:absolute relative lg:bottom-0 w-full">
      <a
        href="../src/account.html"
        class="block px-5 py-6 hover:bg-slate-100 hover:text-slate-900 transition-all"
      >
        <span class="flex items-center gap-6">
          <img src="assets/gear.svg" alt="" />My Account
        </span>
      </a>
    </li>
  </ul>
</nav>

<!-- Overlay -->
<div
  id="overlay"
  onclick="document.getElementById('mobile-menu').classList.add('hidden');
           document.getElementById('overlay').classList.add('hidden')"
  class="fixed inset-0 bg-black/50 z-30 hidden lg:hidden"
></div>
