<form hx-post="/api/update-name" hx-target="#name-field">
  <div class="flex gap-2 items-center w-full">
    <input
      type="text"
      name="name"
      class="w-full border rounded px-2 py-1"
      value="<PERSON>"
      autofocus
    />
    <button type="submit" class="text-blue-500 hover:underline font-bold">
      Save
    </button>
    <button
      type="button"
      class="text-gray-500 hover:underline"
      hx-get="components/name-display-field.html"
      hx-target="#name-field"
    >
      Cancel
    </button>
  </div>
</form>
<button
  id="change-name-btn"
  class="opacity-0 font-bold text-blue-500 hover:underline w-1/5 text-right"
  hx-swap-oob="true"
>
  Change
</button>
