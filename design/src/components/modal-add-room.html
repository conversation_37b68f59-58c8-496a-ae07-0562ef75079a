<div class="fixed inset-0 z-50">
  <!-- Dark overlay with click handler to close -->
  <div
    class="fixed inset-0 bg-black/50 pointer-events-auto"
    hx-get=""
    hx-target="#modal"
    hx-swap="innerHTML"
    hx-trigger="click"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Edit Unit</h3>
          <p>description here</p>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          hx-get=""
          hx-target="#modal"
          hx-swap="innerHTML"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
        <form>
          <div class="flex flex-wrap">
            <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="unit">Room Name</label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="unit"
                  value="Unit 1"
                />
              </div>
            </div>
            <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
              <div class="flex flex-col w-full">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">
                  Leaseholder
                </h2>
                <div class="flex justify-between">
                  <!-- If no leaseholder, say "Vacant" -->
                  John Smith

                  <!-- If no leaseholder, say "create lease", when clicked, switch modal to create lease with the property + room information filled in -->
                  <a href="lease-detail.html">
                    <button
                      class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900 inline-block text-center"
                    >
                      View Lease
                    </button>
                  </a>
                </div>
              </div>
            </div>
            <div class="w-full mt-8">
              <label class="w-full" for="unit">
                <h2 class="text-lg font-semibold text-gray-900">Room Notes</h2>
                <p class="text-sm text-gray-500 mb-4">
                  A place to keep notes for internal purposes. Visible to only
                  you and your team.
                </p>
              </label>
              <textarea
                class="p-4 w-full border border-gray-300 rounded-md focus:outline-none h-48 resize-none"
                id="unit"
                placeholder="Add notes about the tenant here"
              ></textarea>
            </div>
          </div>
        </form>
        <!-- Delete, hide this when creating a room-->
        <div class="w-full mt-8 mb-4">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Delete Room</h2>
          <button
            class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap mb-32"
            hx-get="components/modal-small.html"
            hx-target="#modal"
            hx-swap="innerHTML"
          >
            Delete Room
          </button>
        </div>
      </div>

      <!-- Modal Footer -->
      <div
        class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          hx-get=""
          hx-target="#modal"
          hx-swap="innerHTML"
        >
          Cancel
        </button>
        <button
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Create Room
        </button>
      </div>
    </div>
  </div>
</div>
