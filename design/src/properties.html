<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Properties</title>
    <link href="output.css" rel="stylesheet" />
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
  </head>
  <body class="min-h-screen">
    <div class="flex">
      <!-- Nav bar -->
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>

      <!-- Main Content -->
      <main
        class="h-screen w-full bg-slate-100 overflow-auto lg:px-16 md:px-8 px-4"
      >
        <div class="max-w-[1440px] w-full mx-auto mb-32">
          <!-- Header Section -->
          <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
            <h1 class="text-2xl">My Properties</h1>

            <!-- Search Bar -->
            <div class="mt-8 w-full">
              <div class="flex gap-2">
                <label class="input">
                  <svg
                    class="h-[1em] opacity-50"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <g
                      stroke-linejoin="round"
                      stroke-linecap="round"
                      stroke-width="2.5"
                      fill="none"
                      stroke="currentColor"
                    >
                      <circle cx="11" cy="11" r="8"></circle>
                      <path d="m21 21-4.3-4.3"></path>
                    </g>
                  </svg>
                  <input
                    type="search"
                    class="grow"
                    placeholder="Search for a property or name"
                  />
                </label>
                <button
                  class="hidden sm:block bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm whitespace-nowrap"
                >
                  Search
                </button>
              </div>
            </div>

            <!-- Filter and Create Section -->
            <div
              class="flex flex-col justify-between sm:items-center sm:flex-row w-full gap-4 sm:mt-8 mt-4"
              >
              <!-- Filter By -->
              <div class="flex flex-row gap-4 items-center">
                <span class="text-sm text-gray-500 whitespace-nowrap"
                  >Filter By</span
                >
                <select class="select">
                  <option selected>Current Leases</option>
                  <option>Past Leases</option>
                  <option>Show All</option>
                </select>
              </div>

              <!-- Create Property Button -->
              <div class="w-fit flex justify-end">
                <button
                  class="flex items-center whitespace-nowrap bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 gap-2 rounded text-sm"
                  hx-get="components/modal-create-lease.html"
                  hx-target="#modal"
                  hx-swap="innerHTML"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Create Property
                </button>
              </div>
            </div>
          </div>

          <!-- Desktop View -->
          <div class="hidden lg:block">
            <table
              class="w-full text-sm rounded-md overflow-hidden mt-4"
              style="box-shadow: 0 0 0 1px rgb(203 213 225)"
            >
              <thead>
                <tr
                  class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300"
                >
                  <th class="p-4 w-3/12">
                    <span class="flex items-center gap-2">
                      Property Name
                      <img
                        src="assets/Selector.svg"
                        alt="Sort"
                        class="w-4 h-4"
                      />
                    </span>
                  </th>
                  <th class="p-4">
                    <span class="flex items-center gap-2">
                      Units
                      <img
                        src="assets/Selector.svg"
                        alt="Sort"
                        class="w-4 h-4"
                      />
                    </span>
                  </th>
                  <th class="p-4">
                    <span class="flex items-center gap-2">
                      Vacant
                      <img
                        src="assets/Selector.svg"
                        alt="Sort"
                        class="w-4 h-4"
                      />
                    </span>
                  </th>
                  <th class="p-4">
                    <span class="flex items-center gap-2">
                      Balance
                      <img
                        src="assets/Selector.svg"
                        alt="Sort"
                        class="w-4 h-4"
                      />
                    </span>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  class="bg-white hover:bg-slate-100 text-left group cursor-pointer text-gray-500"
                  hx-get="/property-detail.html"
                  hx-push-url="true"
                  hx-trigger="click"
                >
                  <td class="p-4">
                    <a href="property-detail.html"
                      ><div class="flex items-center">
                        <svg
                          class="w-5 h-5 text-gray-500 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8V7a4 4 0 00-8 0v4"
                          />
                        </svg>
                        <span class="font-medium">Property Name</span>
                      </div></a
                    >
                  </td>
                  <td class="p-4">2</td>
                  <td class="p-4">1</td>
                  <td class="p-4">$0</td>
                </tr>
                <!-- Additional rows can be added here -->
                <tr
                  class="bg-slate-50 hover:bg-slate-100 text-left group cursor-pointer border-t border-slate-300 text-gray-500"
                  hx-get="/property-detail.html"
                  hx-push-url="true"
                  hx-trigger="click"
                >
                  <td class="p-4">
                    <a href="property-detail.html"
                      ><div class="flex items-center">
                        <svg
                          class="w-5 h-5 text-gray-500 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8V7a4 4 0 00-8 0v4"
                          />
                        </svg>
                        <span class="font-medium">Property Name</span>
                      </div></a
                    >
                  </td>
                  <td class="p-4">2</td>
                  <td class="p-4">1</td>
                  <td class="p-4">$0</td>
                </tr>
                <tr
                  class="bg-white hover:bg-slate-100 text-left group cursor-pointer border-slate-300 border-t"
                  hx-get="/property-detail.html"
                  hx-push-url="true"
                  hx-trigger="click"
                >
                  <td class="p-4">
                    <a href="property-detail.html"
                      ><span class="flex items-center gap-2"
                        >Property Name</span
                      ></a
                    >
                  </td>
                  <td class="p-4">2</td>
                  <td class="p-4">1</td>
                  <td class="p-4">$0</td>
                </tr>
              </tbody>
            </table>

            <!-- Desktop Pagination -->
       <div class="hidden lg:flex justify-between items-center gap-4 px-4 py-2 mt-4 bg-white border border-gray-300 rounded-lg">
        <span class="text-sm text-gray-500">Showing 3 of 3 entries</span>
        <div class="join">
         <button class="join-item btn">1</button>
         <button class="join-item btn btn-active">2</button>
         <button class="join-item btn">3</button>
         <button class="join-item btn">4</button>
       </div>
      </div>

          <!-- Mobile/Tablet View -->
          <div class="lg:hidden">
            <div
              class="mt-4 rounded-md overflow-hidden"
              style="box-shadow: 0 0 0 1px rgb(203 213 225)"
            >
              <a href="property-detail.html"
                ><div class="flex flex-wrap p-4 bg-white cursor-pointer group">
                  <span class="font-medium w-full mb-2">Property Name</span>
                  <div class="flex justify justify-between w-full">
                    <span>Units Owned: 2</span>
                    <span>Balance: $100</span>
                  </div>
                  <span>Vacant Units: 1</span>
                </div></a
              >
            </div>

            <!-- Mobile Pagination -->
            <div
              class="flex justify-center items-center gap-4 px-4 py-2 mt-4 bg-white border rounded-lg"
            >
              <button class="p-2 border rounded-md">
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <span class="text-sm">1 of 1</span>
              <button class="p-2 border rounded-md">
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Modal Container -->
    <div id="modal" class=""></div>
  </body>
</html>