<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forum</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200">
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar-tenant.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>
    
    <!-- Right Content Box -->
    <main class="h-screen w-full bg-slate-100 overflow-auto lg:px-16 md:px-8 px-4">
        <div class="max-w-[1440px] w-full mx-auto mb-32">
            <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
                <h1 class="text-2xl">Current Lease</h1>
                <div class="flex w-full items-center bg-white border rounded-md p-6 mt-4 justify-between">
                    <div class="flex gap-4 item-center">
                        <img src="./assets/img-placeholder.png" class="w-16 h-16">
                        <div class="flex flex-col justify-center">
                            <h2 class="text-lg font-semibold">1234 Mississauga Road, Oakville - Unit 321</h2>
                            <div class="flex gap-4 text-sm"><p>January 1, 2024 - Present</p><p>Landlordname lastname</p></div>
                        </div>
                    </div>
                    <div><p>Balance: $0</p><p class="italic text-sm">Paid on time</p></div>
                </div>
            </div>

            <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
                <div class="flex justify-between">
                    <h1 class="text-xl">Past Leases</h1> 
                    <button class="bg-white border text-black px-4 py-2 rounded">
                        Download Records
                      </button>
                </div>
                <div class="flex w-full items-center bg-white border rounded-md p-6 mt-4 justify-between">
                    <div class="flex gap-4 item-center">
                        <img src="./assets/img-placeholder.png" class="w-16 h-16">
                        <div class="flex flex-col justify-center">
                            <h2 class="text-lg font-semibold">1234 Mississauga Road, Oakville - Unit 321</h2>
                            <div class="flex gap-4 text-sm"><p>January 1, 2024 - Present</p><p>Landlordname lastname</p></div>
                        </div>
                    </div>
                    <div></div>
                </div>
            </div>
        </div>
    </main>
    <!-- Modal Container -->
    <div id="modal" class="relative"></div>

    <script>
    function switchTab(tabId) {
        // Hide all content sections
        const contents = document.querySelectorAll('[id^="rent-"], [id^="credit-"], [id^="rental-"], [id^="record-"]');
        contents.forEach(content => {
            content.classList.add('hidden');
        });
        
        // Show selected content
        document.getElementById(tabId).classList.remove('hidden');
        
        // Reset all tabs to default state
        const tabs = document.querySelectorAll('button');
        tabs.forEach(tab => {
            // Reset to default state with hover effects
            tab.className = "w-1/4 px-4 py-4 text-gray-700 hover:text-gray-700 border-b-2 border-transparent hover:border-slate-400 hover:bg-gray-50";
        });
        
        // Style active tab
        const activeTab = document.querySelector(`button[onclick="switchTab('${tabId}')"]`);
        activeTab.className = "w-1/4 px-4 py-4 text-black border-b-2 border-black hover:text-gray-700 bg-gray-50";
    }

    // Set initial active tab
    document.addEventListener('DOMContentLoaded', function() {
        switchTab('rent-reporting');
    });
    </script>
  </body>
</html>