<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200">
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>
    
    <!-- Right Content Box -->
    <main class="h-screen w-full bg-slate-100 overflow-auto lg:px-16 md:px-8 px-4">
        <div class="max-w-[1440px] w-full mx-auto mb-32">
          <div class="w-full mt-20 md:mt-8 pb-0">
            <a class="text-sm underline" href="./admin-user-page.html"
              >← Back to User Details</a
            >
          </div>
            <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
                <h1 class="text-2xl">Billing History for Firstname Lastname</h1>
                
                <div class="block">
                    <table
                      class="table-fixed w-full text-sm rounded-md overflow-hidden mt-4"
                      style="box-shadow: 0 0 0 1px rgb(203 213 225)"
                    >
                      <thead>
                        <tr
                          class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300"
                        >
                          <th class="p-4 w-3/4">
                            <span class="flex items-center gap-2">
                              Purchase
                              <img
                                src="assets/Selector.svg"
                                alt="Sort"
                                class="w-4 h-4"
                              />
                            </span>
                          </th>
                          <th class="p-4 w-1/5">
                            <span class="flex items-center gap-2">
                              Date
                              <img
                                src="assets/Selector.svg"
                                alt="Sort"
                                class="w-4 h-4"
                              />
                            </span>
                          </th>
                          <th class="p-4 w-1/5">
                            <span class="flex items-center gap-2">
                              Status
                              <img
                                src="assets/Selector.svg"
                                alt="Sort"
                                class="w-4 h-4"
                              />
                            </span>
                          </th>
                          <th class="p-4 w-1/5">
                            <span class="flex items-center gap-2">
                              Amount
                              <img
                                src="assets/Selector.svg"
                                alt="Sort"
                                class="w-4 h-4"
                              />
                            </span>
                          </th>
                          <th class="p-4 w-1/5">
                            <span class="flex items-center gap-2">
                              Payment
                              <img
                                src="assets/Selector.svg"
                                alt="Sort"
                                class="w-4 h-4"
                              />
                            </span>
                          </th>
                          <th class="p-4 w-1/5">
                            <span class="flex items-center gap-2">
                              Refund
                              <img
                                src="assets/Selector.svg"
                                alt="Sort"
                                class="w-4 h-4"
                              />
                            </span>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          class="bg-white hover:bg-slate-100 text-left group cursor-pointer text-gray-500"
                        >
                          <td class="p-4 w-full">
                            Renewed investor account
                          </td>
                          <td class="p-4 max-w-[149px]">Feb 2, 2025</td>
                          <td class="p-4 max-w-[149px]"><div class="badge badge-soft badge-primary">Paid</div></td>
                          <td class="p-4 max-w-[149px]">$19.99</td>
                          <td class="p-4 max-w-[149px]">VISA1234</td>
                          <td class="p-4 max-w-[149px]">
                            <button class="btn btn-xs"
                              hx-get="./components/modal-small.html"
                              hx-target="#modal"
                              hx-swap="innerHTML"
                              hx-trigger="click"
                            >refund</button>
                          </td>
                          </tr> 
                        <tr
                          class="bg-white hover:bg-slate-100 text-left group cursor-pointer text-gray-500"
                        >
                          <td class="p-4 w-full">
                            Renewed investor account
                          </td>
                          <td class="p-4 max-w-[149px]">Feb 1, 2025</td>
                          <td class="p-4 max-w-[149px]"><div class="badge badge-soft badge-error">Failed</div></td>
                          <td class="p-4 max-w-[149px]">$19.99</td>
                          <td class="p-4 max-w-[149px]">VISA1234</td>
                          <td class="p-4 max-w-[149px]">-
                          </td>
                          </tr> 
                        <tr
                          class="bg-white hover:bg-slate-100 text-left group cursor-pointer text-gray-500"
                        >
                          <td class="p-4 w-full">
                            Upgraded account to Investor (monthly billing)
                          </td>
                          <td class="p-4 max-w-[149px]">Jan 1, 2025</td>
                          <td class="p-4 max-w-[149px]"><div class="badge badge-soft badge-primary">Paid</div></td>
                          <td class="p-4 max-w-[149px]">$19.99</td>
                          <td class="p-4 max-w-[149px]">VISA1234</td>
                          <td class="p-4 max-w-[149px]">-</td>
                          </tr> 
                      </tbody>
                    </table>
        
                    <!-- Desktop Pagination -->
               <div class="hidden lg:flex justify-between items-center gap-4 px-4 py-2 mt-4 bg-white border border-gray-300 rounded-lg">
                <span class="text-sm text-gray-500">Showing 3 of 3 entries</span>
                <div class="join">
                 <button class="join-item btn">1</button>
                 <button class="join-item btn btn-active">2</button>
                 <button class="join-item btn">3</button>
                 <button class="join-item btn">4</button>
                </div>
              </div>
        </div>
    </main>
    <!-- Modal Container -->
    <div id="modal" ></div>

    <script>
    function closeModal() { 
        document.getElementById('modal').innerHTML = '';
    }

    function switchTab(tabId) {
        // Hide all content sections
        const contents = document.querySelectorAll('[id^="rent-"], [id^="credit-"], [id^="rental-"], [id^="record-"]');
        contents.forEach(content => {
            content.classList.add('hidden');
        });
        
        // Show selected content
        document.getElementById(tabId).classList.remove('hidden');
        
        // Reset all tabs to default state
        const tabs = document.querySelectorAll('#tab-container button');
        tabs.forEach(tab => {
            // Reset to default state with hover effects
            tab.className = "w-1/4 px-4 py-4 text-gray-700 hover:text-gray-700 border-b-2 border-transparent hover:border-slate-400 hover:bg-gray-50";
        });
        
        // Style active tab
        const activeTab = document.querySelector(`button[onclick="switchTab('${tabId}')"]`);
        activeTab.className = "w-1/4 px-4 py-4 text-black border-b-2 border-black hover:text-gray-700 bg-gray-50";
    }

    // Set initial active tab
    document.addEventListener('DOMContentLoaded', function() {
        switchTab('rent-reporting');
    });
    </script>
  </body>
</html>