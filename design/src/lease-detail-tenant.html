<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Lease Record</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
  </head>
  <body class="min-h-screen bg-gray-200">
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>

      <main class="w-full overflow-y-auto lg:px-16 md:px-8 px-4 bg-slate-100">
        <!-- Sticky bar-->
        <div
          class="fixed bottom-0 lg:ml-64 right-0 lg:w-[calc(100%-16rem)] w-full z-50"
          hx-get="./components/stickybar.html"
          hx-trigger="load"
          hx-target="this"
          hx-swap="innerHTML"
        >
        </div>

        <!-- Page Content-->
        <div class="max-w-[1440px] w-full mx-auto md:mt-24 lg:mt-4">
          <div class="w-full mt-20 md:mt-8 pb-0">
            <a class="text-sm underline" href="./leases.html"
              >← Back to leases</a
            >
          </div>

          <div class="w-full mt-8 pb-0">
            <h1 class="text-2xl">Lease Record</h1>
          </div>

          <!-- Property Details-->
          <div
            class="flex flex-col lg:flex-row bg-white rounded border border-gray-300 mt-4 w-full p-2 md:p-6 text-sm"
          >
            <!-- Property Details Section -->
            <div class="lg:w-1/2 w-full">
              <div class="flex flex-col w-full gap-4">
                <h2 class="text-lg font-bold w-full mb-4">Property Details</h2>

                <div class="flex flex-col">
                  <label class="mb-2 font-bold" for="property-name"
                    >Property</label
                  >
                  <span class="font-bold">The Laurels (apartment name)</span>
                  <span class="text-gray-500"
                    >1234 NewApartment Road, Oakville
                  </span>
                  <span class="text-gray-500"> X1X 1X1, ON, Canada</span>
                </div>

                <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
                  <div class="flex flex-wrap md:w-1/2 w-full">
                    <label class="w-full font-bold mb-2" for="unit">Unit</label>
                    <span>1</span>
                  </div>
                  <div class="flex flex-wrap md:w-1/2 w-full">
                    <label class="w-full font-bold mb-2" for="unit"
                      >Rent Due</label
                    >
                    <span class="flex flex-row items-center gap-2"
                      ><svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        class="size-4"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"
                        />
                      </svg>
                      1st of every month</span
                    >
                  </div>
                </div>
                <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
                  <div class="flex flex-wrap md:w-1/2 w-full">
                    <label class="w-full font-bold mb-2" for="unit"
                      >Monthly Rent</label
                    >
                    <span class="flex flex-row items-center gap-2"> $2000</span>
                  </div>
                  <div class="flex flex-wrap md:w-1/2 w-full">
                    <label class="w-full font-bold mb-2" for="unit"
                      >Additional Fees</label
                    >
                    <span
                      class="flex flex-row items-center gap-2 text-gray-500"
                    >
                      $0</span
                    >
                  </div>
                </div>
                <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
                  <div class="flex flex-wrap md:w-1/2 w-full">
                    <label class="w-full font-bold mb-2" for="unit"
                      >Lease Status</label
                    >
                    <span class="flex flex-row items-center gap-2">
                      Active</span
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Documents + Options Section -->
            <div
              class="flex flex-col gap-4 lg:ml-8 mt-8 lg:mt-0 w-full lg:w-1/2 h-full"
            >
              <div class="flex flex-col bg-gray-200 p-4 rounded w-full gap-4">
                <span class="font-bold">Lease Document</span>
                <a href="#" class="underline">John Smith Lease Agreement.PDF</a></span>
              </div>
              <div class="flex bg-gray-200 p-4 rounded w-full flex-wrap">
                <span class="font-bold w-full">Options</span>
                <div class="flex flex-col gap-4 py-4 w-full">
                  <!-- First toggle -->
                  <div class="flex items-center justify-between w-full">
                    <label
                      for="rent-reporting"
                      class="flex items-center gap-2 font-medium text-gray-700 w-full"
                    >
                      Rent Reporting

                      <div class="tooltip" data-tip="Enable rent reporting to help tenants build their
                      credit score">
                        <img
                          src="assets/Help-circle.svg"
                          alt=""
                          class="hover:cursor-help"
                        />
                      </div>
                    </label>
                    <span>On</span>
                  </div>

                  <!-- Second toggle -->
                  <div class="flex items-center justify-between w-full">
                    <label
                      for="auto-pay"
                      class="flex items-center gap-2 font-medium text-gray-700 w-full"
                    >
                      Auto-Pay

                      <div class="tooltip" data-tip="Automatically log monthly rent payment at the
                      end of reporting period">
                        <img
                          src="assets/Help-circle.svg"
                          alt=""
                          class="hover:cursor-help"
                        />
                      </div>
                    </label>
                    <span>On</span>
                  </div>
                </div>
              </div>

              <div class="flex bg-gray-200 p-4 rounded w-full flex-wrap">
                <span class="font-bold w-full">Landlord</span>
                <div class="flex flex-col gap-4 py-4 w-full">
                  <span>Firstname Lastname</span>
                  <span class="font-bold">Contact information</span>
                  <div class="flex flex-col xl:flex-row justify-between gap-4">
                    <span class="inline-flex gap-2"
                      ><svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        class="size-6"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"
                        />
                      </svg>

                      <EMAIL></span
                    >

                    <span class="inline-flex gap-2 items-center"
                      ><svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        class="size-6"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"
                        />
                      </svg>
                      416-000-0000</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Tenant + Payments-->
          <div class="flex flex-col lg:flex-row w-full mt-8 gap-8 pb-32">
            <!-- Tenants-->
            <div class="w-full lg:w-1/2">
              <div
                class="w-full inline-flex justify-between mb-2 items-baseline"
              >
                <h2 class="text-lg font-bold w-full mb-4">Tenants</h2>
              </div>
              <div class="w-full">
                <!-- Tenant list-->
                <table
                  class="w-full rounded-md overflow-hidden text-xs"
                  style="box-shadow: 0 0 0 1px rgb(203 213 225)"
                >
                  <tr
                    class="bg-slate-200 text-black uppercase border-b border-slate-300"
                  >
                    <th class="p-4 w-4/12 rounded-tl-md">
                      <span class="flex items-center gap-2"> Name </span>
                    </th>
                    <th class="p-4">
                      <span class="flex items-center gap-2"> Contact </span>
                    </th>
                    <th class="p-4"></th>
                  </tr>
                  <tr
                    class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t p-2"
                    hx-get="components/modal-view-tenant.html"
                    hx-target="#modal"
                    hx-swap="innerHTML"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Tenant N.</span>
                    </td>
                    <td class="p-4"><EMAIL></td>
                    <td>
                      <img src="./assets/more-vertical.svg" class="m-auto" />
                    </td>
                  </tr>
                  <tr
                    class="bg-slate-50 hover:bg-slate-100 text-left group cursor-pointer border-t border-gray-200 p-2"
                    hx-get="components/modal-view-tenant.html"
                    hx-target="#modal"
                    hx-swap="innerHTML"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Tenant N.</span>
                    </td>
                    <td class="p-4"><EMAIL></td>
                    <td>
                      <img src="./assets/more-vertical.svg" class="m-auto" />
                    </td>
                  </tr>
                </table>
              </div>
            </div>

            <!-- Payments-->
            <div class="flex flex-wrap w-full lg:w-1/2 max-h-fit">
              <!-- Header-->
              <div
                class="w-full inline-flex justify-between mb-2 items-baseline"
              >
                <h2 class="text-lg font-bold w-full mb-4">Payments</h2>
              </div>
              <div class="w-full">
                <table
                  class="w-full rounded-md overflow-hidden text-xs shadow-xl"
                  style="box-shadow: 0 0 0 1px rgb(203 213 225)"
                >
                  <tr
                    class="bg-slate-200 text-black uppercase border-b border-slate-300"
                  >
                    <th class="p-4 w-4/12 rounded-tl-md">
                      <span class="flex items-center gap-2"> Date </span>
                    </th>
                    <th class="p-4">
                      <span class="flex items-center gap-2"> Amount </span>
                    </th>
                    <th class="p-4">
                      <span class="flex items-center gap-2"> Balance </span>
                    </th>
                    <th class="p-4"></th>
                  </tr>
                  <tr
                    class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t border-gray-200"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Feb 12, 2025</span>
                    </td>
                    <td class="p-4">$2000</td>
                    <td class="p-4">$100</td>
                    <td></td>
                  </tr>
                  <tr
                    class="bg-slate-50 hover:bg-slate-100 text-left group cursor-pointer border-t border-gray-200"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Feb 12, 2025</span>
                    </td>
                    <td class="p-4">$2000</td>
                    <td class="p-4">$100</td>
                    <td></td>
                  </tr>
                  <tr
                    class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t border-gray-200 text-gray-500"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 12, 2025</span>
                    </td>
                    <td class="p-4">$2000</td>
                    <td class="p-4">$100</td>
                    <td></td>
                  </tr>
                  <tr
                    class="bg-slate-50 hover:bg-slate-100 text-left group cursor-pointer border-t border-gray-200 text-gray-500"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 12, 2025</span>
                    </td>
                    <td class="p-4">$2000</td>
                    <td class="p-4">$100</td>
                    <td></td>
                  </tr>
                  <tr
                    class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t border-gray-200 text-gray-500"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 12, 2025</span>
                    </td>
                    <td class="p-4">$2000</td>
                    <td class="p-4">$100</td>
                    <td></td>
                  </tr>
                </table>
              </div>
            </div>
          </div>

          
      </main>
    </div>

    <datalist id="test">
      <option value="Property 1"></option>
      <option value="Property 2"></option>
      <option value="Property 3"></option>
      <option value="Property 4"></option>
      <option value="Property 5"></option>
      <option value="Property 6"></option>
    </datalist>

    <!-- Modal Container -->
    <div id="modal" class=""></div>
  </body>
</html>