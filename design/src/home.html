<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200">
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>
    
    <!-- Right Content Box -->
    <main class="h-screen w-full bg-slate-100 overflow-auto lg:px-16 md:px-4 px-4">
        <div class="max-w-[1440px] w-full mx-auto mb-32">
            <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
                <h1 class="text-2xl">Account Activity</h1>
                <div class="flex flex-wrap lg:flex-nowrap w-full mt-4 py-4 gap-4 min-h-64">
                    <div class="lg:w-1/2 w-full bg-white border rounded-md p-6">
                        <h2 class="text-lg font-semibold">Account Summary</h2>
                        <table class="w-full bg-white mt-4">
                            <tr>
                                <th class="text-left font-normal py-2">Active Leases</th>
                                <th class="text-right font-normal py-2">123</th>
                            </tr>
                            <tr>
                                <th class="text-left font-normal py-2">Vacant Units</th>
                                <th class="text-right font-normal py-2">123</th>
                            </tr>
                            <tr>
                                <th class="text-left font-normal py-2">Overdue Rent</th>
                                <th class="text-right font-normal py-2">$1,000,000.00</th>
                            </tr>
                        </table>
                    </div>
                    <div class="lg:w-1/2 w-full bg-white border rounded-md p-6">
                        <h2 class="text-lg font-semibold">
                            Referral Code
                        </h2>
                        <div class="flex flex-col gap-6 mt-6">
                            <div class="flex flex-row gap-4 items-center">
                                <span class="text-xl">XXXXXX</span>
                                <button class="btn btn-sm btn-outline">Copy code</button>
                                </div>
                            <p>Share your referral code with your contacts. When they sign up, you both get a discount on your next rent report!</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Tutorial box-->
            <div class="mt-8 bg-white">
                <!-- Tab Buttons -->
                <div id="tab-container" class="flex items-center">
                    <button onclick="switchTab('rent-reporting')" class="w-1/4 px-4 py-4 text-black border-b-2 border-black hover:text-gray-700 hover:bg-gray-50 bg-gray-50">
                        Rent Reporting
                    </button>
                    <button onclick="switchTab('credit-building')" class="w-1/4 px-4 py-4 text-gray-700 hover:text-gray-700 border-b-2 border-transparent hover:border-slate-400 hover:bg-gray-50">
                        Credit Building
                    </button>
                    <button onclick="switchTab('rental-forum')" class="w-1/4 px-4 py-4 text-gray-700 hover:text-gray-700 border-b-2 border-transparent hover:border-slate-400 hover:bg-gray-50">
                        Rental Forum
                    </button>
                    <button onclick="switchTab('record-keeping')" class="w-1/4 px-4 py-4 text-gray-700 hover:text-gray-700 border-b-2 border-transparent hover:border-slate-400 hover:bg-gray-50">
                        Record Keeping
                    </button>
                </div>

                <!-- Tab Content -->
                <div id="rent-reporting" class="flex bg-white px-8 gap-16 pb-16 pt-10 rounded-md">
                    <div class="flex w-full md:w-1/2 flex-col gap-8">
                        <div><button class="bg-black text-white py-2 px-4 text-sm rounded-md whitespace-nowrap" 
                            hx-get="./components/modal-create-lease.html"
                            hx-trigger="click"
                            hx-target="#modal"
                            hx-method="innerHTML">Create Lease</button></div>
                        <div class="flex items-start gap-3">
                            <div class="flex-shrink-0 w-6 h-6 rounded-full bg-slate-100 flex items-center justify-center">
                                <span class="text-sm text-gray-500">1</span>
                            </div>
                            <p class="text-sm">Create a property to get started with your first lease</p>
                        </div>

                        <div class="flex items-start gap-3">
                            <div class="flex-shrink-0 w-6 h-6 rounded-full bg-slate-100 flex items-center justify-center">
                                <span class="text-sm text-gray-500">2</span>
                            </div>
                            <p class="text-sm">Add tenants and assign them to units</p>
                        </div>

                        <div class="flex items-start gap-3">
                            <div class="flex-shrink-0 w-6 h-6 rounded-full bg-slate-100 flex items-center justify-center">
                                <span class="text-sm text-gray-500">3</span>
                            </div>
                            <p class="text-sm">Start collecting rent and managing your properties</p>
                        </div>
                    </div>
                    <div class=" w-full md:w-1/2">
                        <div class="flex flex-col gap-3">
                            <p class=" text-gray-600 leading-relaxed">
                                Enable rent reporting to help your tenants build their credit score.
                            </p>
                            
                            <p class=" text-gray-600 leading-relaxed">
                                Regular, on-time rent payments can be reported to credit bureaus, providing tenants with a valuable opportunity to establish and improve their credit history.
                            </p>
                            
                            <p class=" text-gray-600 leading-relaxed">
                                This feature benefits both landlords and tenants - encouraging timely payments while helping tenants achieve their financial goals.
                            </p>
                        </div>
                    </div>
                </div>

                <div id="credit-building" class="hidden flex bg-white px-8 gap-16 pb-16 pt-10 rounded-md">
                    <div class="flex w-full md:w-1/2 flex-col gap-8">
                        <div class="flex flex-col gap-3">
                            <p class=" text-gray-600 leading-relaxed">
                                Help your tenants understand how credit scores work and improve their financial health.
                            </p>
                            <p class=" text-gray-600 leading-relaxed">
                                Our credit building tools provide educational resources and personalized insights to help tenants make informed financial decisions.
                            </p>
                            <p class=" text-gray-600 leading-relaxed">
                                Track credit score improvements over time and celebrate financial milestones with your tenants.
                            </p>
                        </div>
                    </div>
                </div>

                <div id="rental-forum" class="hidden flex bg-white px-8 gap-16 pb-16 pt-10 rounded-md">
                    <div class="flex w-full md:w-1/2 flex-col gap-8">
                        <div class="flex flex-col gap-3">
                            <p class=" text-gray-600 leading-relaxed">
                                Connect with other landlords and property managers to share experiences and best practices.
                            </p>
                            <p class="  text-gray-600 leading-relaxed">
                                Access a community-driven knowledge base of property management tips, legal updates, and industry trends.
                            </p>
                            <p class="  text-gray-600 leading-relaxed">
                                Participate in discussions about property management strategies and get advice from experienced professionals.
                            </p>
                        </div>
                    </div>
                </div>

                <div id="record-keeping" class="hidden flex bg-white px-8 gap-16 pb-16 pt-10 rounded-md">
                    <div class="flex w-full md:w-1/2 flex-col gap-8">
                        <div class="flex flex-col gap-3">
                            <p class="  text-gray-600 leading-relaxed">
                                Keep all your important documents organized and easily accessible in one secure location.
                            </p>
                            <p class="  text-gray-600 leading-relaxed">
                                Track maintenance requests, lease agreements, and payment histories with our comprehensive record-keeping system.
                            </p>
                            <p class="  text-gray-600 leading-relaxed">
                                Generate detailed reports and maintain accurate records for tax purposes and legal compliance.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <!-- Modal Container -->
    <div id="modal" ></div>

    <script>
    function closeModal() { 
        document.getElementById('modal').innerHTML = '';
    }

    function switchTab(tabId) {
        // Hide all content sections
        const contents = document.querySelectorAll('[id^="rent-"], [id^="credit-"], [id^="rental-"], [id^="record-"]');
        contents.forEach(content => {
            content.classList.add('hidden');
        });
        
        // Show selected content
        document.getElementById(tabId).classList.remove('hidden');
        
        // Reset all tabs to default state
        const tabs = document.querySelectorAll('#tab-container button');
        tabs.forEach(tab => {
            // Reset to default state with hover effects
            tab.className = "w-1/4 px-4 py-4 text-gray-700 hover:text-gray-700 border-b-2 border-transparent hover:border-slate-400 hover:bg-gray-50";
        });
        
        // Style active tab
        const activeTab = document.querySelector(`button[onclick="switchTab('${tabId}')"]`);
        activeTab.className = "w-1/4 px-4 py-4 text-black border-b-2 border-black hover:text-gray-700 bg-gray-50";
    }

    // Set initial active tab
    document.addEventListener('DOMContentLoaded', function() {
        switchTab('rent-reporting');
    });
    </script>
  </body>
</html>