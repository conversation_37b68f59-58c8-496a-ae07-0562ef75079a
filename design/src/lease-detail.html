<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Lease Record</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
  </head>
  <body class="min-h-screen bg-gray-200">
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>

      <main class="w-full overflow-y-auto lg:px-16 md:px-8 px-4 bg-slate-100">
        <!-- Sticky bar-->
        <div
          class="fixed bottom-0 lg:ml-64 right-0 bg-white border lg:w-[calc(100%-16rem)] w-full z-50"
        >
          <div class="flex justify-between py-4 px-2 md:px-8">
            <button class="bg-white border text-black px-4 py-2 rounded">
              Download Record
            </button>
            <button class="bg-slate-800 text-white px-4 py-2 rounded">
              Save
            </button>
          </div>
        </div>

        <!-- Page Content-->
        <div class="max-w-[1440px] w-full mx-auto md:mt-24 lg:mt-4">
          <div class="w-full mt-20 md:mt-8 pb-0">
            <a class="text-sm underline" href="./leases.html"
              >← Back to leases</a
            >
          </div>

          <div class="w-full mt-8 pb-0">
            <h1 class="text-2xl">Lease Record</h1>
          </div>

          <!-- Property Details-->
          <div
            class="flex flex-col lg:flex-row bg-white rounded border border-gray-300 mt-4 w-full p-2 md:p-6 text-sm"
          >
            <!-- Property Details Section -->
            <div class="flex flex-1 flex-wrap">
              <h2 class="text-lg font-bold w-full mb-4">Property Details</h2>

              <label class="mb-2" for="property-name">Property Name</label>
              <select
                class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none appearance-none cursor-pointer"
                id="property-name"
                style="
                  background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M19 9l-7 7-7-7\'%3E%3C/path%3E%3C/svg%3E');
                  background-position: right 8px center;
                  background-repeat: no-repeat;
                  background-size: 20px;
                "
              >
                <option value="">Select a property...</option>
                <option value="property1">Property 1</option>
                <option value="property2">Property 2</option>
                <option value="property3">Property 3</option>
              </select>
              <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
                <div class="flex flex-wrap md:w-1/2 w-full">
                  <label class="w-full mb-2" for="unit">Room</label>
                  <input
                    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                    id="unit"
                    placeholder="Default Unit"
                    value="Unit 2"
                    list="test"
                  />
                </div>
                <div class="flex flex-wrap md:w-1/2 w-full">
                  <label class="w-full mb-2" for="rent-due">Rent Due</label>
                  <div class="relative w-full">
                    <div
                      class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
                    >
                      <svg
                        class="w-5 h-5 text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <input
                      class="p-2 pl-10 py-4 w-full border border-gray-300 rounded-md focus:outline-none [&::-webkit-calendar-picker-indicator]:hidden"
                      id="rent-due"
                      placeholder="1st of Every Month"
                      value="1st of Every Month"
                      type="date"
                      onclick="this.showPicker()"
                    />
                  </div>
                </div>
              </div>
              <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
                <div class="flex flex-wrap md:w-1/2 w-full">
                  <label class="w-full mb-2" for="monthly-rent"
                    >Monthly Rent</label
                  >
                  <div class="relative w-full">
                    <span
                      class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                      >$</span
                    >
                    <input
                      type="text"
                      id="monthly-rent"
                      placeholder="1"
                      value="2000"
                      class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                    />
                  </div>
                </div>
                <div class="flex flex-wrap md:w-1/2 w-full">
                  <label class="w-full mb-2" for="additional-fees"
                    >Extra Monthly Fees</label
                  >
                  <div class="relative w-full">
                    <span
                      class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                      >$</span
                    >
                    <input
                      type="text"
                      id="additional-fees"
                      placeholder="0"
                      class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                    />
                  </div>
                </div>
              </div>
              <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
                <div class="flex flex-wrap md:w-1/2 w-full">
                  <label class="w-full mb-2" for="lease-status"
                    >Lease Status</label
                  >
                  <select
                    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none appearance-none cursor-pointer"
                    id="lease-status"
                    style="
                      background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M19 9l-7 7-7-7\'%3E%3C/path%3E%3C/svg%3E');
                      background-position: right 8px center;
                      background-repeat: no-repeat;
                      background-size: 20px;
                    "
                  >
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                    <option value="Pending">Pending</option>
                  </select>
                </div>
                <div class="flex flex-wrap md:w-1/2 w-full">
                  <label class="w-full mb-2" for="assigned-to"
                    >Assigned To</label
                  >
                  <select
                    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none appearance-none cursor-pointer"
                    id="assigned-to"
                    style="
                      background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M19 9l-7 7-7-7\'%3E%3C/path%3E%3C/svg%3E');
                      background-position: right 8px center;
                      background-repeat: no-repeat;
                      background-size: 20px;
                    "
                  >
                    <option value="">All Agents (Default)</option>
                    <option value="Agent 1">Agent 1</option>
                    <option value="Agent 2">Agent 2</option>
                    <option value="Agent 3">Agent 3</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Documents + Options Section -->
            <div
              class="flex flex-wrap flex-1 lg:ml-8 mt-8 lg:mt-0 w-full h-full"
            >
              <div
                class="flex flex-col bg-gray-200 p-4 rounded w-full mb-4 gap-4"
              >
                <span class="font-bold mb-2">Lease Document</span>
                <div
                  class="py-8 flex flex-col items-center justify-center w-full border-2 border-dashed rounded-md border-gray-500 gap-2"
                >
                  <!-- File Input -->
                  <div id="file-input" class="flex flex-col items-center gap-2">
                    <label
                      class="bg-slate-800 text-white px-4 py-2 rounded-md hover:cursor-pointer"
                    >
                      Choose File
                      <input
                        class="hidden"
                        type="file"
                        onchange="
                        document.getElementById('file-input').classList.add('hidden');
                        document.getElementById('file-preview').classList.remove('hidden');
                        document.getElementById('file-name').textContent = this.files[0]?.name || 'No file chosen';
                        document.getElementById('file-link').href = URL.createObjectURL(this.files[0]);"
                      />
                    </label>
                    <span class="text-gray-500 text-sm">No file chosen</span>
                  </div>

                  <!-- File Preview -->
                  <div
                    id="file-preview"
                    class="hidden flex flex-col items-center gap-3"
                  >
                    <a
                      id="file-link"
                      href="#"
                      target="_blank"
                      class="flex items-center gap-2 hover:bg-gray-100 bg-gray-50 px-3 py-1.5 rounded-md text-gray-700"
                    >
                      <svg
                        class="w-4 h-4 text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                        />
                      </svg>
                      <span id="file-name"></span>
                    </a>
                    <button
                      class="text-sm text-red-600 hover:text-red-800"
                      onclick="
                      document.getElementById('file-preview').classList.add('hidden');
                      document.getElementById('file-input').classList.remove('hidden');
                      document.querySelector('input[type=file]').value = '';
                      URL.revokeObjectURL(document.getElementById('file-link').href);"
                    >
                      Remove file
                    </button>
                  </div>
                </div>
              </div>
              <div class="flex bg-gray-200 p-4 rounded w-full flex-wrap">
                <span class="font-bold w-full">Options</span>
                <div class="flex flex-col gap-4 py-4 w-full">
                  <!-- First toggle -->
                  <div class="flex items-center justify-between w-full">
                    <label
                      for="rent-reporting"
                      class="flex items-center gap-2 font-medium text-gray-700 w-full"
                    >
                      Rent Reporting
                      <div class="group relative inline-block">
                        <img
                          src="assets/Help-circle.svg"
                          alt=""
                          class="hover:cursor-help"
                        />
                        <span
                          class="invisible group-hover:visible opacity-0 group-hover:opacity-100 border border-gray-300 transition bg-white text-black text-sm rounded-md p-2 absolute z-50 -top-16 left-1/2 -translate-x-1/2 w-48 text-center"
                        >
                          Enable rent reporting to help tenants build their
                          credit score
                        </span>
                      </div>
                    </label>
                    <label
                      class="relative inline-flex items-center cursor-pointer"
                    >
                      <input type="checkbox" class="sr-only peer" />
                      <div
                        class="w-11 h-6 bg-gray-100 border border-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                      ></div>
                    </label>
                  </div>

                  <!-- Second toggle -->
                  <div class="flex items-center justify-between w-full">
                    <label
                      for="auto-pay"
                      class="flex items-center gap-2 font-medium text-gray-700 w-full"
                    >
                      Auto-Pay
                      <div class="group relative inline-block">
                        <img
                          src="assets/Help-circle.svg"
                          alt=""
                          class="hover:cursor-help"
                        />
                        <span
                          class="invisible group-hover:visible opacity-0 group-hover:opacity-100 border border-gray-300 transition bg-white text-black text-sm rounded-md p-2 absolute z-50 -top-16 left-1/2 -translate-x-1/2 w-48 text-center"
                        >
                          Set up automatic monthly rent payments
                        </span>
                      </div>
                    </label>
                    <label
                      class="relative inline-flex items-center cursor-pointer"
                    >
                      <input type="checkbox" class="sr-only peer" />
                      <div
                        class="w-11 h-6 bg-gray-100 border border-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                      ></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Tenant + Payments-->
          <div class="flex flex-col lg:flex-row w-full mt-8 gap-8">
            <!-- Tenants-->
            <div class="w-full lg:w-1/2">
              <div
                class="w-full inline-flex justify-between mb-2 items-baseline"
              >
                <h2 class="text-lg font-bold w-full mb-4">Tenants</h2>
                <button
                  hx-get="components/modal-add-tenant.html"
                  hx-target="#modal"
                  hx-swap="innerHTML"
                  class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap"
                >
                  Add Tenant
                </button>
              </div>
              <div class="w-full">
                <!-- Tenant list-->
                <table
                  class="w-full rounded-md overflow-hidden text-xs"
                  style="box-shadow: 0 0 0 1px rgb(203 213 225)"
                >
                  <tr
                    class="bg-slate-200 text-black uppercase border-b border-slate-300"
                  >
                    <th class="p-4 w-4/12 rounded-tl-md">
                      <span class="flex items-center gap-2"> Name </span>
                    </th>
                    <th class="p-4">
                      <span class="flex items-center gap-2"> Contact </span>
                    </th>
                    <th class="p-4">
                      <span class="flex items-center justify-center">
                        Edit
                      </span>
                    </th>
                  </tr>
                  <tr
                    class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t p-2"
                    hx-get="components/modal-add-tenant.html"
                    hx-target="#modal"
                    hx-swap="innerHTML"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Tenant N.</span>
                    </td>
                    <td class="p-4"><EMAIL></td>
                    <td>
                      <img src="./assets/more-vertical.svg" class="m-auto" />
                    </td>
                  </tr>
                  <tr
                    class="bg-slate-50 hover:bg-slate-100 text-left group cursor-pointer border-t p-2"
                    hx-get="components/modal-add-tenant.html"
                    hx-target="#modal"
                    hx-swap="innerHTML"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Tenant N.</span>
                    </td>
                    <td class="p-4"><EMAIL></td>
                    <td>
                      <img src="./assets/more-vertical.svg" class="m-auto" />
                    </td>
                  </tr>
                </table>
              </div>
            </div>

            <!-- Payments-->
            <div class="flex flex-wrap w-full lg:w-1/2 max-h-fit">
              <!-- Header-->
              <div
                class="w-full inline-flex justify-between mb-2 items-baseline"
              >
                <h2 class="text-lg font-bold w-full mb-4">Payments</h2>
                <button
                  hx-get="components/modal-add-payment.html"
                  hx-target="#modal"
                  hx-swap="innerHTML"
                  class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap"
                >
                  Add Payment
                </button>
              </div>
              <div class="w-full">
                <table
                  class="w-full rounded-md overflow-hidden text-xs shadow-xl"
                  style="box-shadow: 0 0 0 1px rgb(203 213 225)"
                >
                  <tr
                    class="bg-slate-200 text-black uppercase border-b border-slate-300"
                  >
                    <th class="p-4 w-4/12 rounded-tl-md">
                      <span class="flex items-center gap-2"> Date </span>
                    </th>
                    <th class="p-4">
                      <span class="flex items-center gap-2"> Amount </span>
                    </th>
                    <th class="p-4">
                      <span class="flex items-center gap-2"> Balance </span>
                    </th>
                    <th class="p-4">
                      <span class="flex items-center justify-center">
                        Edit
                      </span>
                    </th>
                  </tr>
                  <tr
                    class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t"
                    hx-get="components/modal-add-payment.html"
                    hx-target="#modal"
                    hx-swap="innerHTML"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 12, 2025</span>
                    </td>
                    <td class="p-4">$2000</td>
                    <td class="p-4">$100</td>
                    <td>
                      <img src="./assets/more-vertical.svg" class="m-auto" />
                    </td>
                  </tr>
                  <tr
                    class="bg-slate-50 hover:bg-slate-100 text-left group cursor-pointer border-t"
                    hx-get="components/modal-add-payment.html"
                    hx-target="#modal"
                    hx-swap="innerHTML"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 12, 2025</span>
                    </td>
                    <td class="p-4">$2000</td>
                    <td class="p-4">$100</td>
                    <td>
                      <img src="./assets/more-vertical.svg" class="m-auto" />
                    </td>
                  </tr>
                  <tr
                    class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t text-gray-500"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 12, 2025</span>
                    </td>
                    <td class="p-4">$2000</td>
                    <td class="p-4">$100</td>
                    <td></td>
                  </tr>
                  <tr
                    class="bg-slate-50 hover:bg-slate-100 text-left group cursor-pointer border-t text-gray-500"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 12, 2025</span>
                    </td>
                    <td class="p-4">$2000</td>
                    <td class="p-4">$100</td>
                    <td></td>
                  </tr>
                  <tr
                    class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t text-gray-500"
                    class="p-2"
                  >
                    <td class="p-4">
                      <span class="flex items-center gap-2">Jan 12, 2025</span>
                    </td>
                    <td class="p-4">$2000</td>
                    <td class="p-4">$100</td>
                    <td></td>
                  </tr>
                </table>
              </div>
            </div>
          </div>

          <!-- Lease Notes-->
          <div class="w-full mt-8">
            <label class="w-full">
              <h2 class="text-lg font-semibold text-gray-900">Lease Notes</h2>
              <p class="text-sm text-gray-500 mb-4">
                A place to keep notes for internal purposes. Visible to only you
                and your team.
              </p>
            </label>
            <textarea
              class="p-4 w-full border bg-white border-gray-300 rounded-md focus:outline-none h-48 resize-none"
              id="notes"
              placeholder="Add notes about the lease here"
            ></textarea>
          </div>
          <!-- Delete-->
          <div class="w-full mt-8 mb-4">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              Delete Lease
            </h2>
            <button
              class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap mb-32"
              hx-get="components/modal-small.html"
              hx-target="#modal"
              hx-swap="innerHTML"
            >
              Delete Lease
            </button>
          </div>
        </div>
      </main>
    </div>

    <datalist id="test">
      <option value="Property 1"></option>
      <option value="Property 2"></option>
      <option value="Property 3"></option>
      <option value="Property 4"></option>
      <option value="Property 5"></option>
      <option value="Property 6"></option>
    </datalist>

    <!-- Modal Container -->
    <div id="modal" class=""></div>
  </body>
</html>
