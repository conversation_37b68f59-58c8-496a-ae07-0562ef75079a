<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forum</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200">
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>
    
    <!-- Right Content Box -->
    <main class="h-screen w-full bg-slate-100 overflow-auto px-4">
      <div class="flex-1  mt-20 md:mt-8 pb-0 w-full md:px-8 max-w-[1120px] mx-auto mb-32">
        <div class="flex flex-wrap lg:mt-8 mt-24 mb-4 w-full gap-2">
          <h2 class="text-lg font-bold w-full">Search Reports</h2>
          <!-- Search Bar-->
          <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center w-full mt-2 gap-4 sm:gap-0">
            <div class="flex gap-2 w-full sm:w-8/12">
                <input 
                    placeholder="Search for a property or name" 
                    type="text" 
                    class="p-2 px-10 border border-1 rounded text-sm flex-1 w-full sm:max-w-[400px]" 
                    style="background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\'%3E%3C/path%3E%3C/svg%3E');
                    background-position: 8px 8px;
                    background-repeat: no-repeat;
                    background-size: 20px;"
                />
                <button class="hidden sm:block bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm whitespace-nowrap">Search</button> 
            </div>
            <span class="text-sm underline">My Reports</span>
          </div>
        </div>
        
        <!-- Reports Summary -->
        <div class="flex flex-wrap mt-8 w-full">
          <div class="flex justify-between items-center w-full">
            <h2 class="text-lg w-full">Recent Reports</h2>
            <button 
                class="bg-slate-800 text-white px-4 py-2 rounded text-sm whitespace-nowrap"
                hx-get="components/modal-create-report.html"
                hx-target="#modal"
                hx-swap="innerHTML"
                hx-trigger="click"
            >
                Create Report
            </button>
          </div>
          <!-- List of Reports-->
           <!-- DEMO REPORT-->
           <div
           class="flex flex-wrap w-full mt-4 cursor-pointer hover:bg-gray-50"
           hx-get="components/modal-view-report.html"
           hx-target="#modal"
           hx-swap="innerHTML"
           hx-trigger="click"
         >
         <div
         class="flex flex-col w-full bg-white hover:bg-gray-50 rounded border py-6 px-8 gap-2"
         >
         <h3 class="font-bold">
           Destruction of property - 123 oakstreet avenue, Mainfloor
         </h3>
     
         <div class="text-xs w-full flex gap-2">
           <p>Firstname Lastname</p>
           <p>-</p>
           <p>January 12, 2024</p>
         </div>
     
         <p class="text-sm">
           Complaint details goes in here. This is a really quick preview of what the
           thread is about. This sentence will truncate when the preview paragraph is
           two lines.
         </p>
     
         <div class="flex justify-start mt-4">
           <div class="flex w-fit gap-2">
             <button
               class="flex gap-2 py-2 px-3 items-center hover:bg-gray-200 transition-colors rounded-full text-xs"
             >
               <img src="assets/thumbsup.svg" class="w-5 h-5" alt="" />
               <span>25</span>
             </button>
             <div class="flex gap-2 items-center">
               <button
                 class="flex gap-2 p-2 items-center hover:bg-gray-200 transition-colors rounded-full text-xs"
               >
                 <img src="assets/message.svg" class="w-5 h-5" alt="" />
                 <span>300</span>
               </button>
             </div>
           </div>
         </div>
       </div>
           </div>
         </div>
         
          <div class="w-full" hx-get="components/report-card.html" hx-trigger="load" hx-swap="innerHTML"></div>
          <div class="w-full" hx-get="components/report-card.html" hx-trigger="load" hx-swap="innerHTML"></div>
          <div class="w-full" hx-get="components/report-card.html" hx-trigger="load" hx-swap="innerHTML"></div>
          <div class="w-full" hx-get="components/report-card.html" hx-trigger="load" hx-swap="innerHTML"></div>
        </div>
      </div>
    </main>
    <!-- Modal Container -->
    <div id="modal" class="relative"></div>
  </body>
</html>