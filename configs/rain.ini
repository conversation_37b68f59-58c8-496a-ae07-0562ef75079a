[dbs]
verbose = 3

[dbs.rr]
#uri = "mongodb://198.19.249.3:27017,198.19.249.3:27018/rr?replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=1&tls=false"
uri = "mongodb://r1:r1@198.19.249.3:27017/rr?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"
[dbs.rr_test]
#uri = "mongodb://198.19.249.3:27017,198.19.249.3:27018/rr_test?replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=1&tls=false"
uri = "mongodb://r1:r1@198.19.249.3:27017/rr_test?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"


[server]
port = "8089"
baseUrl = "/"
static_path = "/home/<USER>/go-repos/rent_report/src/web/dist"
developer_mode = true

[golog]
dir = "logs"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"

[oauthClient]
clientId = "report_rentals"
clientSecret = "x7QmpLkeW9NuGrVoTkbDyEzXAqdPwtm39QmpLkeb"
redirectUrl = "http://127.0.0.1/v1/auth/oauth/callback"
authUrl = "https://d1w.realmaster.com/provider/authorize"
tokenUrl = "https://d1w.realmaster.com/provider/token"
userInfoUrl = "https://d1w.realmaster.com/provider/userinfo"

[encryption]
key = "b9lzajneH2MiTrUeRzaSyCzCBgaRfib2"

[auth]
jwtSecret = "rUeRzaSyCzCBgaRfib2b9lzajneH2MiT"
