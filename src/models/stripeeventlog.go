package models

import "time"

// StripeEventLog 表示Webhook事件日志
type StripeEventLog struct {
	Eid string    `json:"eid" bson:"_id"`                     // 事件ID（如 evt_1Ox...）
	Tp  string    `json:"tp" bson:"tp"`                       // 事件类型（如 payment_intent.succeeded）
	Sts string    `json:"sts" bson:"sts"`                     // 当前处理状态（如 received, processed, failed）
	Raw string    `json:"raw" bson:"raw"`                     // 原始JSON字符串（可选）
	Pid string    `json:"pid,omitempty" bson:"pid,omitempty"` // 关联的支付ID（如果有）
	Ts  time.Time `json:"ts" bson:"ts"`                       // 接收时间（CreatedAt）
	Mt  time.Time `json:"mt,omitempty" bson:"mt,omitempty"`   // 更新时间（处理后可更新）
}
