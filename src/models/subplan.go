package models

import "time"

// SubPlan 表示订阅计划
type SubPlan struct {
	PlanId string    `json:"planId" bson:"_id"`                    // 计划ID Plan ID（或 Stripe Price ID）
	Nm     string    `json:"nm" bson:"nm"`                         // 计划名称 Plan Name
	Prc    int64     `json:"prc" bson:"prc"`                       // 价格（分） Price in cents
	Cur    string    `json:"cur" bson:"cur"`                       // 币种 Currency
	Intrvl string    `json:"intrvl" bson:"intrvl"`                 // 计费周期 Billing Interval (month/year)
	Dscp   string    `json:"dscp,omitempty" bson:"dscp,omitempty"` // 描述 Description
	PrdId  string    `json:"prdId" bson:"prdId"`                   // Stripe产品ID Stripe Product ID
	PrcId  string    `json:"prcId" bson:"prcId"`                   // Stripe价格ID Stripe Price ID
	Sts    string    `json:"sts" bson:"sts"`                       // 状态 Status
	Ts     time.Time `json:"ts" bson:"ts"`                         // 创建时间 Created Timestamp
	Mt     time.Time `json:"mt" bson:"mt"`                         // 更新时间 Modified Timestamp
}
