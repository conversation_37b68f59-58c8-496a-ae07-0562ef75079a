package models

import "time"

// UserSub 表示用户与订阅的实际关系
// subId: Stripe订阅ID，uid: 用户ID，planId: 订阅计划ID，sts: 状态，ts: 创建时间，mt: 更新时间
// 状态如 active, canceled, trial 等

type UserSub struct {
	subId  string    `json:"subId" bson:"_id"`     // Stripe订阅ID
	uid    string    `json:"uid" bson:"uid"`       // 用户ID
	planId string    `json:"planId" bson:"planId"` // 订阅计划ID
	sts    string    `json:"sts" bson:"sts"`       // 状态
	ts     time.Time `json:"ts" bson:"ts"`         // 创建时间
	mt     time.Time `json:"mt" bson:"mt"`         // 更新时间
}
