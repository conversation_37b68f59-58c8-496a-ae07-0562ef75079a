package models

// User 表示系统中的用户
type User struct {
	ID    string
	Email string
	Role  string
}

// 用户角色常量
const (
	RoleNormalUser = "normal_user"
	RoleAdmin      = "admin"
	RoleDev        = "dev"
	RoleSuperAdmin = "super_admin"
)

// 权限常量
const (
	ActionView   = "view"
	ActionCreate = "create"
	ActionUpdate = "update"
	ActionDelete = "delete"
	ActionManage = "manage"
	ActionAccess = "access"
)

// 资源常量
const (
	ResourceProperty = "property"
	ResourceUser     = "user"
	ResourceAdmin    = "admin"
	ResourceDev      = "dev"
	ResourceAPI      = "api"
	ResourceConfig   = "config"
)
