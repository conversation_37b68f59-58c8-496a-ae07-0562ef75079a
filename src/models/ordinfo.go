package models

import "time"

// OrdInfo 表示订单信息
type OrdInfo struct {
	Oid    string    `json:"oid" bson:"_id"`                         // 订单ID Order ID
	Uid    string    `json:"uid" bson:"uid"`                         // 用户ID User ID
	PrdId  string    `json:"prdId" bson:"prdId"`                     // 商品ID Product or Service ID
	Qty    int       `json:"qty" bson:"qty"`                         // 购买数量 Quantity
	TtlAmt int64     `json:"ttlAmt" bson:"ttlAmt"`                   // 总金额（分） Total Amount in cents
	Cur    string    `json:"cur" bson:"cur"`                         // 币种 Currency
	Sts    string    `json:"sts" bson:"sts"`                         // 状态 Status（如 paid、pending）
	SsnId  string    `json:"ssnId,omitempty" bson:"ssnId,omitempty"` // Stripe会话ID Checkout Session ID (可选)
	Ts     time.Time `json:"ts" bson:"ts"`                           // 创建时间 Created Timestamp
	Mt     time.Time `json:"mt" bson:"mt"`                           // 更新时间 Modified Timestamp
}
