package models

import "time"

// PayInfo 表示支付信息
type PayInfo struct {
	Pid            string                 `json:"pid" bson:"_id"`                                           // 支付ID Payment ID
	Uid            string                 `json:"uid" bson:"uid"`                                           // 用户ID User ID
	CusId          string                 `json:"cusId" bson:"cusId"`                                       // Stripe客户ID Stripe Customer ID
	PmId           string                 `json:"pmId" bson:"pmId"`                                         // 支付方式ID Payment Method ID
	CrdBrd         string                 `json:"crdBrd" bson:"crdBrd"`                                     // 卡片品牌 Card Brand (Visa等)
	CrdL4          string                 `json:"crdL4" bson:"crdL4"`                                       // 卡号后四位 Last 4 digits of card
	ExpMn          int                    `json:"expMn" bson:"expMn"`                                       // 到期月份 Expiry Month
	ExpYr          int                    `json:"expYr" bson:"expYr"`                                       // 到期年份 Expiry Year
	MtdTp          string                 `json:"mtdTp" bson:"mtdTp"`                                       // 支付方式类型 Payment Method Type (如 card)
	BillingAddress map[string]interface{} `json:"billingAddress,omitempty" bson:"billingAddress,omitempty"` // 账单地址 Billing Address
	Ts             time.Time              `json:"ts" bson:"ts"`                                             // 创建时间 Created Timestamp
}
