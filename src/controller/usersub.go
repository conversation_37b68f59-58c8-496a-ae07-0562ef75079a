package controller

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"time"

	"rent_report/entities"
	"rent_report/router"
	"rent_report/utils"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go"
	"github.com/stripe/stripe-go/sub"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type UserSubController struct{}

func init() {
	router.Register(&UserSubController{})
}

func (c *UserSubController) RegisterRoutes(r *gin.Engine) {
	r.GET("/v1/usersub", c.handleGetUserSubs)

	// RESTful subscription cancellation
	r.DELETE("/v1/subscriptions/:id", c.handleCancelUserSubRESTful)

	// Deprecated route (for backward compatibility)
	r.POST("/v1/usersub/cancel", c.handleCancelUserSub)
}

func (c *UserSubController) handleGetUserSubs(ctx *gin.Context) {
	uid, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil || uid == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized or invalid token"})
		return
	}
	cursor, err := gomongo.Coll("rr", "usersub").Find(ctx.Request.Context(), bson.M{"uid": uid})
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch user subscriptions"})
		return
	}
	defer cursor.Close(ctx.Request.Context())
	var subs []bson.M
	if err := cursor.All(ctx.Request.Context(), &subs); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decode user subscriptions"})
		return
	}
	ctx.JSON(http.StatusOK, subs)
}

func (c *UserSubController) handleCancelUserSub(ctx *gin.Context) {
	// 1. 验证用户身份
	uid, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil || uid == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized or invalid token"})
		return
	}

	var req struct {
		UserSubId   string `json:"usersubId"`
		StripeSubId string `json:"stripeSubId"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request parameters"})
		return
	}
	if req.UserSubId == "" || req.StripeSubId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing required parameters"})
		return
	}

	// 2. 验证订阅所有权 - 确认要取消的订阅属于当前用户
	filter := bson.M{"_id": req.UserSubId, "uid": uid}
	var userSub bson.M
	err = gomongo.Coll("rr", "usersub").FindOne(ctx.Request.Context(), filter).Decode(&userSub)
	if err != nil {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Subscription not found or access denied"})
		return
	}

	// 3. 取消 Stripe 订阅
	stripe.Key = os.Getenv("STRIPE_SECRET_KEY")
	_, err = sub.Cancel(
		req.StripeSubId,
		nil,
	)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel Stripe subscription: " + err.Error()})
		return
	}

	// 4. 更新 usersub 状态为 canceled - 使用包含用户ID的filter确保安全
	update := bson.M{"$set": bson.M{"sts": "canceled"}}
	_, err = gomongo.Coll("rr", "usersub").UpdateOne(ctx.Request.Context(), filter, update)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update database: " + err.Error()})
		return
	}

	// 5. 发送订阅取消确认邮件
	if user, err := entities.GetUserByID(ctx.Request.Context(), uid); err == nil {
		userEmail := user.Email
		userName := user.Username
		if userEmail != "" {
			golog.Debug("Sending subscription cancelled email (manual cancellation)", "userEmail", userEmail, "userName", userName)
			_ = utils.SendSubscriptionCancelledEmail(userEmail, userName)
		}
	}

	// 6. 处理租约的 rent reporting 关闭和邮件通知
	err = handleUserSubscriptionCancellation(ctx.Request.Context(), uid)
	if err != nil {
		// 记录错误但不影响主要操作
		golog.Error("Failed to handle rent reporting cancellation", "error", err, "userId", uid)
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true})
}

// DELETE /v1/subscriptions/:id - RESTful version
func (c *UserSubController) handleCancelUserSubRESTful(ctx *gin.Context) {
	// 1. 验证用户身份
	uid, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil || uid == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized or invalid token"})
		return
	}

	// 2. 从URL参数获取订阅ID
	subscriptionID := ctx.Param("id")
	if subscriptionID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "subscription ID is required"})
		return
	}

	// 3. 查找用户的订阅记录
	filter := bson.M{"uid": uid, "stripeSubId": subscriptionID}
	var userSub bson.M
	err = gomongo.Coll("rr", "usersub").FindOne(ctx.Request.Context(), filter).Decode(&userSub)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "subscription not found"})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find subscription: " + err.Error()})
		}
		return
	}

	// 4. 取消 Stripe 订阅
	stripe.Key = os.Getenv("STRIPE_SECRET_KEY")
	_, err = sub.Cancel(
		subscriptionID,
		nil,
	)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel Stripe subscription: " + err.Error()})
		return
	}

	// 5. 更新 usersub 状态为 canceled
	update := bson.M{"$set": bson.M{"sts": "canceled"}}
	_, err = gomongo.Coll("rr", "usersub").UpdateOne(ctx.Request.Context(), filter, update)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update database: " + err.Error()})
		return
	}

	// 6. 发送订阅取消确认邮件
	if user, err := entities.GetUserByID(ctx.Request.Context(), uid); err == nil {
		userEmail := user.Email
		userName := user.Username
		if userEmail != "" {
			golog.Debug("Sending subscription cancelled email (RESTful cancellation)", "userEmail", userEmail, "userName", userName)
			_ = utils.SendSubscriptionCancelledEmail(userEmail, userName)
		}
	}

	// 7. 处理租约的 rent reporting 关闭和邮件通知
	err = handleUserSubscriptionCancellation(ctx.Request.Context(), uid)
	if err != nil {
		// 记录错误但不影响主要操作
		golog.Error("Failed to handle rent reporting cancellation", "error", err, "userId", uid)
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true})
}

// handleUserSubscriptionCancellation 处理用户订阅取消时的租约 rent reporting 关闭和邮件通知
func handleUserSubscriptionCancellation(ctx context.Context, userID string) error {
	// 这个函数与 stripe_webhook.go 中的 handleSubscriptionCancellationRentReporting 功能相同
	// 但为了避免循环导入，在这里重新实现

	leasesColl := gomongo.Coll("rr", "leases")
	if leasesColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 查找该用户所有启用了 rent reporting 的租约
	filter := bson.M{
		"usrId":   userID,
		"rentRep": true,
	}

	cursor, err := leasesColl.Find(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to find leases: %v", err)
	}
	defer cursor.Close(ctx)

	var leasesToUpdate []entities.Lease
	if err = cursor.All(ctx, &leasesToUpdate); err != nil {
		return fmt.Errorf("failed to decode leases: %v", err)
	}

	// 批量更新租约状态
	if len(leasesToUpdate) > 0 {
		updateFilter := bson.M{"usrId": userID, "rentRep": true}
		update := bson.M{"$set": bson.M{"rentRep": false, "mt": time.Now()}}
		res, err := leasesColl.UpdateMany(ctx, updateFilter, update)
		if err != nil {
			return fmt.Errorf("failed to update leases: %v", err)
		}
		golog.Debug("Updated leases rentRep status", "matched", res.MatchedCount, "modified", res.ModifiedCount)

		// 为每个租约发送暂停邮件通知
		for _, originalLease := range leasesToUpdate {
			// 创建更新后的租约对象
			updatedLease := originalLease
			updatedLease.RentReporting = false

			// 发送租金报告暂停通知邮件
			err = sendRentReportingPausedNotificationForUserSubLease(ctx, &originalLease)
			if err != nil {
				// 记录错误但不影响主要操作
				golog.Error("Failed to send rent reporting paused notification emails", "error", err, "leaseId", originalLease.ID)
			}
		}
	}

	return nil
}

// sendRentReportingPausedNotificationForUserSubLease 为用户订阅取消发送租金报告暂停通知
func sendRentReportingPausedNotificationForUserSubLease(ctx context.Context, originalLease *entities.Lease) error {
	// 获取房东信息
	user, err := entities.GetUserByID(ctx, originalLease.UserID)
	if err != nil {
		golog.Error("Failed to get landlord user info", "error", err, "userId", originalLease.UserID)
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 获取属性信息
	property, err := entities.GetProperty(ctx, originalLease.PropertyID, originalLease.UserID)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", originalLease.PropertyID)
		// 不返回错误，继续发送邮件
	}

	// 准备房东信息
	landlordInfo := utils.LandlordInfo{
		FirstName:       user.Username,   // 使用 username 作为名字
		LastName:        "",              // 没有单独的姓氏字段
		PropertyAddress: "your property", // 默认值
	}

	// 如果成功获取到属性信息，格式化属性地址
	if property != nil {
		addr := property.Address
		addressStr := ""
		if addr.Street != "" {
			addressStr += addr.Street
		}
		if addr.City != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.City
		}
		if addr.Prov != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Prov
		}
		if addr.ZipCode != "" {
			if addressStr != "" {
				addressStr += " "
			}
			addressStr += addr.ZipCode
		}
		if addr.Country != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Country
		}

		if addressStr != "" {
			landlordInfo.PropertyAddress = addressStr
		}
	}

	// 如果房东姓名为空，使用默认值
	if landlordInfo.FirstName == "" {
		landlordInfo.FirstName = "Your landlord"
	}

	// 准备租户邮件信息
	var tenants []utils.TenantEmailInfo
	for _, tenant := range originalLease.CurrentTenants {
		if tenant.Email != "" && tenant.FirstName != "" {
			tenants = append(tenants, utils.TenantEmailInfo{
				Email:     tenant.Email,
				FirstName: tenant.FirstName,
			})
		}
	}

	if len(tenants) > 0 {
		// 异步发送暂停邮件
		utils.SendRentReportingPausedNotificationEmailsAsync(tenants, landlordInfo)
		golog.Info("Initiated rent reporting paused notification emails (user subscription cancelled)",
			"leaseId", originalLease.ID,
			"tenantCount", len(tenants),
			"landlordName", landlordInfo.FirstName,
			"propertyAddress", landlordInfo.PropertyAddress,
			"propertyId", originalLease.PropertyID)
	} else {
		golog.Warn("No valid tenant emails found for rent reporting paused notification", "leaseId", originalLease.ID)
	}

	return nil
}
