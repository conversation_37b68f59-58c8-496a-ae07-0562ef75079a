package controller

import (
	"net/http"
	"rent_report/router"

	"github.com/gin-gonic/gin"
)

type SystemController struct{}

func init() {
	router.Register(&SystemController{})
}

func (c *SystemController) RegisterRoutes(r *gin.Engine) {
	r.GET("/health", c.handleHealth)
	r.GET("/v1/health", c.handleHealth) // Also add v1 version for consistency
}

func (c *SystemController) handleHealth(ctx *gin.Context) {
	// Get the query URL
	queryURL := ctx.Request.URL.String()

	ctx.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"url":    queryURL,
	})
}
