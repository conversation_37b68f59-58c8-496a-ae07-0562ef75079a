package auth

import (
	"fmt"
	"path/filepath"
	"runtime"

	"rent_report/models"

	"github.com/casbin/casbin/v2"
	"github.com/casbin/casbin/v2/model"
	fileadapter "github.com/casbin/casbin/v2/persist/file-adapter"
)

var (
	enforcer *casbin.Enforcer
)

// getProjectRoot 获取项目根目录
func getProjectRoot() string {
	_, b, _, _ := runtime.Caller(0)
	return filepath.Join(filepath.Dir(b), "../..")
}

// InitCasbin initializes the Casbin enforcer
func InitCasbin() error {
	// 获取项目根目录
	rootDir := getProjectRoot()

	// Load model from file
	modelPath := filepath.Join(rootDir, "configs", "rbac_model.conf")
	policyPath := filepath.Join(rootDir, "configs", "rbac_policy.csv")

	m, err := model.NewModelFromFile(modelPath)
	if err != nil {
		return fmt.Errorf("failed to load model: %v", err)
	}

	// Create enforcer
	adapter := fileadapter.NewAdapter(policyPath)
	enforcer, err = casbin.NewEnforcer(m, adapter)
	if err != nil {
		return fmt.Errorf("failed to create enforcer: %v", err)
	}

	// Load policies
	if err := enforcer.LoadPolicy(); err != nil {
		return fmt.Errorf("failed to load policy: %v", err)
	}

	return nil
}

// CheckPermission checks if a user has permission to perform an action
func CheckPermission(role, domain, object, action string) bool {
	if enforcer == nil {
		return false
	}

	// If role is empty, treat as normal_user
	if role == "" {
		role = models.RoleNormalUser
	}

	// Check permission
	allowed, err := enforcer.Enforce(role, domain, object, action)
	if err != nil {
		return false
	}

	return allowed
}

// AddRole adds a role to a user
func AddRole(userID, role string) error {
	if enforcer == nil {
		return fmt.Errorf("enforcer not initialized")
	}

	_, err := enforcer.AddGroupingPolicy(userID, role, "*")
	return err
}

// RemoveRole removes a role from a user
func RemoveRole(userID, role string) error {
	if enforcer == nil {
		return fmt.Errorf("enforcer not initialized")
	}

	_, err := enforcer.RemoveGroupingPolicy(userID, role, "*")
	return err
}

// GetUserRoles returns all roles of a user
func GetUserRoles(userID string) ([]string, error) {
	if enforcer == nil {
		return nil, fmt.Errorf("enforcer not initialized")
	}

	return enforcer.GetRolesForUser(userID, "*")
}

// AddPermission adds a permission to a role
func AddPermission(role, domain, object, action string) error {
	if enforcer == nil {
		return fmt.Errorf("enforcer not initialized")
	}

	_, err := enforcer.AddPolicy(role, domain, object, action)
	return err
}

// RemovePermission removes a permission from a role
func RemovePermission(role, domain, object, action string) error {
	if enforcer == nil {
		return fmt.Errorf("enforcer not initialized")
	}

	_, err := enforcer.RemovePolicy(role, domain, object, action)
	return err
}

// GetRolePermissions returns all permissions of a role
func GetRolePermissions(role string) ([][]string, error) {
	if enforcer == nil {
		return nil, fmt.Errorf("enforcer not initialized")
	}

	return enforcer.GetPermissionsForUser(role, "*")
}
