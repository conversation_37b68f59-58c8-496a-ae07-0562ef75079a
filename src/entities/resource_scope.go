package entities

import (
	"context"
	"fmt"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	AccountTypeFree     = "f" // Shorter value for free account type
	AccountTypeBusiness = "b" // Shorter value for business account type
)

// ResourceScope defines the access scope of a resource (personal or organizational)
type ResourceScope struct {
	UserID         string `bson:"usrId,omitempty"`
	OrganizationID string `bson:"orgId,omitempty"`
}

// GetResourceScope determines the scope based on user's account type
func GetResourceScope(ctx context.Context, userID string) (*ResourceScope, error) {
	var user struct {
		AccountType    string `bson:"acctTp"`
		OrganizationID string `bson:"orgId,omitempty"`
	}

	userColl := gomongo.Coll("rr", "users")
	err := userColl.FindOne(ctx, bson.M{"_id": userID}).Decode(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to get user details: %v", err)
	}

	scope := &ResourceScope{}

	// If user has an organization, they can access org resources regardless of account type
	// If the orgId field doesn't exist in the MongoDB document, it will set user.OrganizationID to empty string from the bson decoder
	if user.OrganizationID != "" {
		scope.OrganizationID = user.OrganizationID
		return scope, nil
	}

	// No organization - personal access only
	scope.UserID = userID
	scope.OrganizationID = "" // Explicitly ensure no org access

	return scope, nil
}

// ToFilter converts the scope to a MongoDB filter
func (s *ResourceScope) ToFilter() bson.M {
	if s.OrganizationID != "" {
		// If user has org access, only filter by organization
		return bson.M{"orgId": s.OrganizationID}
	}
	// Personal access - filter by user
	return bson.M{"usrId": s.UserID}
}
