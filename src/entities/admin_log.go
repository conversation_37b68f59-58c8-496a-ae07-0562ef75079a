package entities

import (
	"context"
	"fmt"
	"time"

	"github.com/real-rm/gomongo"
)

// AdminLog 表示管理员操作日志
// 字段均为缩写，便于存储和传输
// _id: 主键，MongoDB自动生成
// act: 操作类型
// chg: 变更内容
// userId: 被操作用户id
// adminId: 管理员id
// ts: 时间戳

type AdminLog struct {
	ID      string    `bson:"_id,omitempty" json:"_id"`
	Act     string    `bson:"act" json:"act"`
	Chg     string    `bson:"chg" json:"chg"`
	UserId  string    `bson:"userId" json:"userId"`
	AdminId string    `bson:"adminId" json:"adminId"`
	Ts      time.Time `bson:"ts" json:"ts"`
}

// InsertAdminLog 插入一条管理员日志
func InsertAdminLog(ctx context.Context, log *AdminLog) error {
	coll := gomongo.Coll("rr", "admin_logs")
	if coll == nil {
		return fmt.Errorf("admin_logs collection not initialized")
	}
	_, err := coll.InsertOne(ctx, log)
	return err
}

// ListAdminLogs 分页查询管理员日志
func ListAdminLogs(ctx context.Context, skip, limit int64) ([]*AdminLog, error) {
	coll := gomongo.Coll("rr", "admin_logs")
	if coll == nil {
		return nil, fmt.Errorf("admin_logs collection not initialized")
	}
	cur, err := coll.Find(ctx, struct{}{}, nil, skip, limit, "-ts")
	if err != nil {
		return nil, err
	}
	var logs []*AdminLog
	if err := cur.All(ctx, &logs); err != nil {
		return nil, err
	}
	return logs, nil
}

// ListAdminLogsByUserId 按userId查询admin log
func ListAdminLogsByUserId(ctx context.Context, userId string) ([]*AdminLog, error) {
	coll := gomongo.Coll("rr", "admin_logs")
	if coll == nil {
		return nil, fmt.Errorf("admin_logs collection not initialized")
	}
	cur, err := coll.Find(ctx, map[string]interface{}{"userId": userId}, nil, 0, 100, "-ts")
	if err != nil {
		return nil, err
	}
	var logs []*AdminLog
	if err := cur.All(ctx, &logs); err != nil {
		return nil, err
	}
	return logs, nil
}
