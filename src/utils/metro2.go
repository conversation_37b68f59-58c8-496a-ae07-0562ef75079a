package utils

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/moov-io/metro2/pkg/file"
	"github.com/moov-io/metro2/pkg/utils"
)

// GenerateMetro2File takes a JSON string and returns the Metro2 file content
func GenerateMetro2File(jsonStr string) ([]byte, error) {
	// Create a new metro2 file
	metroFile, err := file.NewFile(utils.CharacterFileFormat)
	if err != nil {
		return nil, fmt.Errorf("failed to create metro file: %v", err)
	}

	// Unmarshal the json data into the metro file
	if err := json.Unmarshal([]byte(jsonStr), metroFile); err != nil {
		return nil, fmt.Errorf("failed to unmarshal json: %v", err)
	}

	// Generate and set the trailer record
	trailer, err := metroFile.GeneratorTrailer()
	if err != nil {
		return nil, fmt.Errorf("failed to generate trailer: %v", err)
	}
	if err := metroFile.SetRecord(trailer); err != nil {
		return nil, fmt.Errorf("failed to set trailer record: %v", err)
	}

	// Validate the file
	if err := metroFile.Validate(); err != nil {
		return nil, fmt.Errorf("metro2 file validation failed: %v", err)
	}

	// Get the string representation with proper Metro2 format
	metroString := metroFile.String(true)

	// Make sure the last line has the same length as the previous line
	lines := strings.Split(metroString, "\n")
	previousLineLength := len(lines[len(lines)-2])
	previousLineFirst4Chars := lines[len(lines)-2][:4]

	// Replace first 4 chars of last line with first 4 chars of previous line
	lines[len(lines)-1] = previousLineFirst4Chars + lines[len(lines)-1][4:]
	// Pad the last line to match the previous line's length
	lines[len(lines)-1] = lines[len(lines)-1] + strings.Repeat(" ", previousLineLength-len(lines[len(lines)-1]))
	// Ensure we join with newlines and add a final newline
	metroString = strings.Join(lines, "\n") + "\n"
	// Ensure CRLF line endings
	metroString = strings.ReplaceAll(metroString, "\n", "\r\n")
	// Fix any double CRLF that might have been created
	metroString = strings.ReplaceAll(metroString, "\r\r\n", "\r\n")

	// Return as bytes
	return []byte(metroString), nil
}
