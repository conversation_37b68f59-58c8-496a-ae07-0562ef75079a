package utils

import (
	"context"
	"fmt"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

var resourceCollections = []string{
	"properties",
	"rooms",
	"leases",
	"tenants",
	"payments",
}

// MigrateToOrganization moves all resources from personal to organization ownership
func MigrateToOrganization(ctx context.Context, userID string, organizationID string) error {
	for _, collName := range resourceCollections {
		if err := migrateCollectionToOrg(ctx, collName, userID, organizationID); err != nil {
			return fmt.Errorf("failed to migrate %s: %v", collName, err)
		}
	}
	return nil
}

// MigrateToPersonal moves all organization resources to personal ownership
func MigrateToPersonal(ctx context.Context, userID string, organizationID string) error {
	for _, collName := range resourceCollections {
		if err := migrateCollectionToPersonal(ctx, collName, userID, organizationID); err != nil {
			return fmt.E<PERSON><PERSON>("failed to migrate %s: %v", collName, err)
		}
	}
	return nil
}

// Helper function to migrate a collection to organization ownership
func migrateCollectionToOrg(ctx context.Context, collName string, userID string, organizationID string) error {
	coll := gomongo.Coll("rr", collName)
	if coll == nil {
		return fmt.Errorf("%s collection not initialized", collName)
	}

	filter := bson.M{
		"usrId": userID,
		"orgId": "",
	}

	update := bson.M{
		"$set": bson.M{
			"orgId": organizationID,
		},
	}

	_, err := coll.UpdateMany(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to migrate %s to organization: %v", collName, err)
	}

	return nil
}

// Helper function to migrate a collection to personal ownership
func migrateCollectionToPersonal(ctx context.Context, collName string, userID string, organizationID string) error {
	coll := gomongo.Coll("rr", collName)
	if coll == nil {
		return fmt.Errorf("%s collection not initialized", collName)
	}

	filter := bson.M{
		"orgId": organizationID,
	}

	update := bson.M{
		"$set": bson.M{
			"orgId": "",
			"usrId": userID,
		},
	}

	_, err := coll.UpdateMany(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to migrate %s to personal: %v", collName, err)
	}

	return nil
}
