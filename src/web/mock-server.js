// 模拟延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟 API 响应
export const mockResponses = {
  '/api/html/news': async () => {
    await delay(1000); // 模拟网络延迟
    return `
      <div class="space-y-4">
        <div class="bg-white rounded-lg shadow p-6">
          <span class="inline-block px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded mb-2">科技创新</span>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">人工智能技术取得重大突破</h3>
          <p class="text-gray-600 mb-4">我们的研发团队在机器学习领域取得重大进展，新算法在图像识别准确率上提升了30%...</p>
          <div class="flex justify-between items-center text-sm text-gray-500">
            <span>2024-01-20</span>
            <a href="#" class="text-blue-600 hover:text-blue-800">阅读更多</a>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <span class="inline-block px-2 py-1 text-xs font-semibold bg-green-100 text-green-800 rounded mb-2">企业动态</span>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">公司荣获"年度最佳雇主"奖项</h3>
          <p class="text-gray-600 mb-4">凭借优秀的企业文化和员工福利政策，我们在2024年度最佳雇主评选中脱颖而出...</p>
          <div class="flex justify-between items-center text-sm text-gray-500">
            <span>2024-01-15</span>
            <a href="#" class="text-blue-600 hover:text-blue-800">阅读更多</a>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <span class="inline-block px-2 py-1 text-xs font-semibold bg-purple-100 text-purple-800 rounded mb-2">产品发布</span>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">全新产品线正式发布</h3>
          <p class="text-gray-600 mb-4">经过一年的研发，我们的新一代产品线今日正式发布，带来多项革命性的改进和创新...</p>
          <div class="flex justify-between items-center text-sm text-gray-500">
            <span>2024-01-10</span>
            <a href="#" class="text-blue-600 hover:text-blue-800">阅读更多</a>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <span class="inline-block px-2 py-1 text-xs font-semibold bg-yellow-100 text-yellow-800 rounded mb-2">市场动态</span>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">成功进军东南亚市场</h3>
          <p class="text-gray-600 mb-4">继国内市场取得成功后，我们的产品正式进入东南亚市场，首月销售额超预期...</p>
          <div class="flex justify-between items-center text-sm text-gray-500">
            <span>2024-01-05</span>
            <a href="#" class="text-blue-600 hover:text-blue-800">阅读更多</a>
          </div>
        </div>
      </div>
    `;
  },

  '/api/data/list': async () => {
    await delay(500); // 模拟较短的网络延迟
    return {
      code: 0,
      data: {
        products: [
          { name: "智能手机 Pro Max", id: 1 },
          { name: "超薄笔记本电脑", id: 2 },
          { name: "无线降噪耳机", id: 3 },
          { name: "智能手表 Series 5", id: 4 },
          { name: "4K 高清显示器", id: 5 }
        ],
        totalUsers: 12580,
        activeOrders: 368,
        completedTasks: 8964
      },
      message: 'success'
    };
  }
}; 