
<div class="max-w-[1440px] w-full mx-auto md:mt-24 lg:mt-4 pb-20">
  <div class="w-full mt-20 md:mt-8 pb-0">
    <a class="text-sm underline cursor-pointer" href="../pages/leases/" onclick="event.preventDefault(); history.back();">← Back to leases</a>
  </div>
  <div class="w-full mt-8 pb-0">
    <h1 class="text-2xl mb-4">Lease Record</h1>
  </div>
  <!-- Property Details-->
  <div class="flex flex-col lg:flex-row bg-white rounded border border-gray-300 mt-4 w-full p-6 text-sm gap-6">
    <!-- 左侧 Property Details -->
    <div class="lg:w-1/2 w-full">
      <h2 class="text-lg font-bold mb-4">Property Details</h2>
      <div class="mb-4">
        <div class="font-bold">Property</div>
        <div>{{propertyAddress}}</div>
      </div>
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div class="font-bold">Unit</div>
          <div>{{roomName}}</div>
        </div>
        <div>
          <div class="font-bold">Rent Due</div>
          <div class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5" />
            </svg>
            {{rentDueDayWithSuffix}} of every month
          </div>
        </div>
        <div>
          <div class="font-bold">Monthly Rent</div>
          <div>${{rentAmount}}</div>
        </div>
        <div>
          <div class="font-bold">Additional Fees</div>
          <div>${{additionalMonthlyFees}}</div>
        </div>
        <div>
          <div class="font-bold">Lease Status</div>
          <div>{{status}}</div>
        </div>
      </div>
    </div>
    <!-- 右侧三卡片 -->
    <div class="flex flex-col gap-4 flex-1">
      <!-- Lease Document Section -->
      <div class="bg-gray-100 p-4 rounded">
        <div class="flex justify-between items-center mb-2">
          <div class="font-bold">Lease Documents</div>
          <span class="text-sm text-gray-600">{{documentCount}} files</span>
        </div>
        <div id="lease-document-display" class="space-y-2 max-h-40 overflow-y-auto">
          {{#documents}}
          <a href="/v1/leases/download/{{id}}" target="_blank" class="flex items-center gap-2 hover:bg-gray-100 bg-gray-50 px-3 py-1.5 rounded-md text-gray-700 block">
            <svg class="w-4 h-4 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium text-gray-900 truncate">{{fileName}}</div>
              <div class="text-xs text-gray-500">{{formattedFileSize}}</div>
            </div>
          </a>
          {{/documents}}
          {{^documents}}
          <div class="py-8 flex flex-col items-center justify-center w-full border-2 border-dashed rounded-md border-gray-500 gap-2">
            <span class="text-gray-500 text-sm">No files uploaded</span>
          </div>
          {{/documents}}
        </div>
      </div>
      <div class="bg-gray-100 p-4 rounded">
        <div class="font-bold mb-2">Options</div>
        <div class="flex flex-col gap-4">
          <!-- Rent Reporting -->
          <div class="flex items-center justify-between">
            <span class="flex items-center gap-2">Rent Reporting</span>
            <span>{{#rentReporting}}On{{/rentReporting}}{{^rentReporting}}Off{{/rentReporting}}</span>
          </div>
          <!-- Auto-Pay -->
          <div class="flex items-center justify-between">
            <span class="flex items-center gap-2">Auto-Pay</span>
            <span>{{#autoPay}}On{{/autoPay}}{{^autoPay}}Off{{/autoPay}}</span>
          </div>
        </div>
      </div>
      <div class="bg-gray-100 p-4 rounded">
        <div class="font-bold mb-2">Landlord</div>
        <div>{{landlordName}}</div>
        <div>{{landlordContact}}</div>
      </div>
    </div>
  </div>

  <!-- Tenants & Payments -->
  <div class="flex flex-col lg:flex-row w-full mt-8 gap-8">
    <!-- Tenants -->
    <div class="w-full lg:w-1/2">
      <h2 class="text-lg font-bold">Tenants</h2>

      <!-- Tenant Type Toggle -->
      <div class="w-full mb-4">
        <div class="min-h-[32px] flex items-center">
          <button
            id="tenant-toggle-btn-tenant-view"
            class="text-sm text-gray-600 hover:text-gray-800 underline cursor-pointer"
          >
            Show past tenants
          </button>
        </div>
      </div>

      <div id="tenant-list-tenant-view">
        <table class="w-full rounded-md overflow-hidden text-xs border">
          <thead>
            <tr class="bg-slate-200 text-black uppercase border-b border-slate-300">
              <th class="p-4">Tenant Name</th>
              <th class="p-4">Email</th>
            </tr>
          </thead>
          <tbody>
            {{#tenants}}
            <tr class="bg-white">
              <td class="p-4">{{name}}</td>
              <td class="p-4">{{email}}</td>
            </tr>
            {{/tenants}}
            {{^tenants}}
            <tr class="bg-white">
              <td class="p-4 text-gray-400" colspan="2">No tenants</td>
            </tr>
            {{/tenants}}
          </tbody>
        </table>
      </div>
    </div>
    <!-- Payments -->
    <div class="w-full lg:w-1/2">
      <h2 class="text-lg font-bold">Payments</h2>

      <!-- Spacer to align with tenant toggle area -->
      <div class="w-full mb-4">
        <div class="min-h-[32px] flex items-center"></div>
      </div>

      <table class="w-full rounded-md overflow-hidden text-xs border">
        <thead>
          <tr class="bg-slate-200 text-black uppercase border-b border-slate-300">
            <th class="p-4">Date</th>
            <th class="p-4">Amount</th>
            <th class="p-4">Remaining Balance</th>
          </tr>
        </thead>
        <tbody>
          {{#payments}}
          <tr class="bg-white">
            <td class="p-4">{{date}}</td>
            <td class="p-4">${{amount}}</td>
            <td class="p-4">${{remainingBalance}}</td>
          </tr>
          {{/payments}}
          {{^payments}}
          <tr class="bg-white">
            <td class="p-4 text-gray-400" colspan="3">No payments</td>
          </tr>
          {{/payments}}
        </tbody>
      </table>
    </div>
  </div>
  <!-- Sticky bar (tenant view) -->
  <div class="fixed bottom-0 lg:ml-64 right-0 bg-white border lg:w-[calc(100%-16rem)] w-full z-20">
    <div class="flex justify-between py-4 px-2 md:px-8">
      <button
        id="download-pdf-btn"
        class="bg-white border text-black px-4 py-2 rounded hover:bg-gray-50"
      >
        Download As PDF
      </button>
      <div></div> <!-- 空的 div 用于保持布局平衡 -->
    </div>
  </div>
</div> 