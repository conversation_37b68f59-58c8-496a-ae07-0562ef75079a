{{#if isNewMessage}}
<!-- 发送新消息表单 -->
<div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
<div class="fixed inset-0 z-10 overflow-y-auto">
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
            <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                <button type="button" class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                        hx-get="/templates/close-modal"
                        hx-target="#modal">
                    <span class="sr-only">Close</span>
                    <i class="mdi mdi-close"></i>
                </button>
            </div>
            <div class="sm:flex sm:items-start">
                <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                    <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Send New Message</h3>
                    <form hx-post="/api/messages/send" hx-target="#modal" class="mt-4">
                        <!-- 收件人选择 -->
                        <div class="mb-4">
                            <label for="recipients" class="block text-sm font-medium text-gray-700">To</label>
                            <div class="mt-1">
                                <div class="flex flex-wrap gap-2 p-2 border rounded-md" id="recipients-container">
                                    <input type="text" 
                                           id="recipient-search"
                                           class="flex-1 min-w-[200px] border-0 focus:ring-0 p-0"
                                           placeholder="Type to search recipients..."
                                           hx-get="/api/users/search"
                                           hx-trigger="keyup changed delay:500ms"
                                           hx-target="#recipient-suggestions">
                                </div>
                                <div id="recipient-suggestions" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md"></div>
                            </div>
                        </div>

                        <!-- 主题选择 -->
                        <div class="mb-4">
                            <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
                            <select id="subject" name="subject" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                <option value="">Select a template</option>
                                {{#each templates}}
                                <option value="{{value}}">{{label}}</option>
                                {{/each}}
                            </select>
                        </div>

                        <!-- 消息内容 -->
                        <div class="mb-4">
                            <label for="body" class="block text-sm font-medium text-gray-700">Message</label>
                            <textarea id="body" name="body" rows="4" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                    placeholder="Type your message here..."></textarea>
                        </div>

                        <!-- 按钮组 -->
                        <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                            <button type="submit"
                                    class="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 sm:ml-3 sm:w-auto">
                                Send Message
                            </button>
                            <button type="button"
                                    class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                                    hx-get="/templates/close-modal"
                                    hx-target="#modal">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{{else}}
<!-- 查看消息详情 -->
<div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
<div class="fixed inset-0 z-10 overflow-y-auto">
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
            <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                <button type="button" class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                        hx-get="/templates/close-modal"
                        hx-target="#modal">
                    <span class="sr-only">Close</span>
                    <i class="mdi mdi-close"></i>
                </button>
            </div>
            <div class="sm:flex sm:items-start">
                <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                    <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">{{subject}}</h3>
                    <div class="mt-2">
                        <p class="text-sm text-gray-500">From: {{from}}</p>
                        <p class="text-sm text-gray-500">Date: {{date}}</p>
                    </div>
                    <div class="mt-4">
                        <p class="text-sm text-gray-700">{{body}}</p>
                    </div>
                    <div class="mt-5 sm:mt-4">
                        <button type="button"
                                class="inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:w-auto"
                                hx-get="/templates/close-modal"
                                hx-target="#modal">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{/if}} 