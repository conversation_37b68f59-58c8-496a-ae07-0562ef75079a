<!-- Property Details Section -->
<div class="flex flex-1 flex-wrap">
  <h2 class="text-lg font-bold w-full mb-4">Property Details</h2>

  <label class="mb-2" for="property-name" label="Property Name">Property Name</label>
  <input
    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
    id="property-name"
    placeholder="Create property name"
    value="{{name}}"
  />

  <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
    <div class="flex flex-wrap md:w-1/2 w-full">
      <label class="w-full mb-2" for="street">Address</label>
      <input
        class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
        id="street"
        placeholder="Street Address"
        value="{{address.street}}"
      />
    </div>
    <div class="flex flex-wrap md:w-1/2 w-full">
      <label class="w-full mb-2" for="unit">Unit</label>
      <input
        class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
        id="unit"
        placeholder=""
        value="{{address.unit}}"
      />
    </div>
  </div>
  <div class="w-full flex gap-4 mt-4">
    <div class="flex flex-wrap w-full">
      <label class="w-full mb-2" for="city">City/Town</label>
      <input
        class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
        id="city"
        placeholder=""
        value="{{address.city}}"
      />
    </div>
  </div>
  <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
    <div class="flex flex-wrap md:w-1/3 w-full">
      <label class="w-full mb-2" for="country">Country</label>
      <input
        class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-gray-100"
        id="country"
        placeholder=""
        value="Canada"
        readonly
      />
    </div>
    <div class="flex flex-wrap md:w-1/3 w-full">
      <label class="w-full mb-2" for="province">Province<span id="province-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
      <select
        class="p-2 w-full border border-gray-300 rounded-md focus:outline-none bg-white h-[52px]"
        id="province"
      >
        <option value="">Select Province</option>
        <option value="AB" {{#isAB}}selected{{/isAB}}>AB - Alberta</option>
        <option value="BC" {{#isBC}}selected{{/isBC}}>BC - British Columbia</option>
        <option value="MB" {{#isMB}}selected{{/isMB}}>MB - Manitoba</option>
        <option value="NB" {{#isNB}}selected{{/isNB}}>NB - New Brunswick</option>
        <option value="NL" {{#isNL}}selected{{/isNL}}>NL - Newfoundland and Labrador</option>
        <option value="NS" {{#isNS}}selected{{/isNS}}>NS - Nova Scotia</option>
        <option value="ON" {{#isON}}selected{{/isON}}>ON - Ontario</option>
        <option value="PE" {{#isPE}}selected{{/isPE}}>PE - Prince Edward Island</option>
        <option value="QC" {{#isQC}}selected{{/isQC}}>QC - Quebec</option>
        <option value="SK" {{#isSK}}selected{{/isSK}}>SK - Saskatchewan</option>
        <option value="NT" {{#isNT}}selected{{/isNT}}>NT - Northwest Territories</option>
        <option value="NU" {{#isNU}}selected{{/isNU}}>NU - Nunavut</option>
        <option value="YT" {{#isYT}}selected{{/isYT}}>YT - Yukon</option>
      </select>
    </div>
    <div class="flex flex-wrap md:w-1/3 w-full">
      <label class="w-full mb-2" for="postal-code">Postal Code<span id="postal-code-error" class="text-red-500 text-xs ml-2 hidden">Invalid Canadian postal code</span></label>
      <input
        class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
        id="postal-code"
        placeholder=""
        value="{{address.zipCode}}"
      />
    </div>
  </div>
  <label class="mt-4 mb-2 w-full" for="status">Property Status</label>
  <select
    class="p-2 w-full border border-gray-300 rounded-md focus:outline-none bg-white h-[52px]"
    id="status"
  >
    <option value="active" {{#isActive}}selected{{/isActive}}>Active</option>
    <option value="inactive" {{#isInactive}}selected{{/isInactive}}>Inactive</option>
    <option value="archived" {{#isArchived}}selected{{/isArchived}}>Archived</option>
  </select>
</div> 