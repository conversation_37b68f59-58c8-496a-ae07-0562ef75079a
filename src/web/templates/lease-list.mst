{{#propertyGroups}}
<tr class="bg-white hover:bg-slate-50 text-left group cursor-pointer border-t property-group" data-property-name="{{propertyName}}">
  <td class="p-4">
    <span class="flex items-center gap-2 font-medium w-full truncate">
      <svg class="group-arrow w-5 h-5 text-gray-500 transform flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
      <span class="truncate">{{propertyName}}</span>
    </span>
  </td>
  <td class="p-4"></td>
  <td class="p-4"></td>
  <td class="p-4"></td>
  <td class="p-4">${{totalOwingBalance}}</td>
</tr>
{{#leases}}
<tr class="hidden bg-white hover:bg-slate-50 text-left">
  <td class="p-4 pl-10">
    <a href="../lease_detail/?id={{id}}" class="flex items-center gap-2">
      <svg class="w-5 h-5 {{#isLeaseActive}}text-green-600{{/isLeaseActive}}{{^isLeaseActive}}text-gray-500{{/isLeaseActive}} flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
        {{#isLeaseActive}}
        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
        {{/isLeaseActive}}
        {{^isLeaseActive}}
        <path stroke-linecap="round" stroke-linejoin="round" d="m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
        {{/isLeaseActive}}
      </svg>
      <span>{{tenantName}}</span>
    </a>
  </td>
  <td class="p-4">
    <a href="../lease_detail/?id={{id}}" class="block">{{roomName}}</a>
  </td>
  <td class="p-4">
    <a href="../lease_detail/?id={{id}}" class="block">{{lastPaymentDate}}</a>
  </td>
  <td class="p-4">
    <a href="../lease_detail/?id={{id}}" class="block">{{#isActive}}On{{/isActive}}{{^isActive}}Off{{/isActive}}</a>
  </td>
  <td class="p-4">
    <a href="../lease_detail/?id={{id}}" class="block">${{owingBalance}}</a>
  </td>
</tr>
{{/leases}}
{{/propertyGroups}}
{{^propertyGroups}}
<tr class="bg-white text-center">
  <td colspan="5" class="p-4 text-gray-500">No leases found</td>
</tr>
{{/propertyGroups}} 