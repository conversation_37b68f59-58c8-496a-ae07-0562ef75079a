{{! Error messages for various components }}

{{! Error Messages Template }}

{{#accountError}}
<div class="text-red-500 p-4 bg-red-50 rounded">
    <p>Failed to load account information. Please try refreshing the page or check if you're logged in.</p>
</div>
{{/accountError}}

{{#nameUpdateError}}
<div class="text-red-500 p-4 bg-red-50 rounded">
    <p>Failed to update name. Please try again.</p>
</div>
{{/nameUpdateError}}

{{#emailUpdateError}}
<div class="text-red-500 p-4 bg-red-50 rounded">
    <p>Failed to update email. Please try again.</p>
</div>
{{/emailUpdateError}}

{{#addressUpdateError}}
<div class="text-red-500 p-4 bg-red-50 rounded">
    <p>Failed to update address. Please try again.</p>
</div>
{{/addressUpdateError}}

{{#phoneUpdateError}}
<div class="text-red-500 p-4 bg-red-50 rounded">
    <p>Failed to update phone number. Please try again.</p>
</div>
{{/phoneUpdateError}}

{{#passwordUpdateError}}
<div class="text-red-500 p-4 bg-red-50 rounded">
    <p>Failed to update password. Please try again.</p>
</div>
{{/passwordUpdateError}}

{{#verificationError}}
<div class="text-red-500 p-4 bg-red-50 rounded">
    <p>Failed to start verification process. Please try again.</p>
</div>
{{/verificationError}}

{{#accountCloseError}}
<div class="text-red-500 p-4 bg-red-50 rounded">
    <p>Failed to close account. Please try again.</p>
</div>
{{/accountCloseError}}

{{#propertyListError}}
<tr class="bg-white text-center">
    <td colspan="4" class="p-4 text-red-500">Failed to load properties. Please try refreshing the page or check if you're logged in.</td>
</tr>
{{/propertyListError}}

{{#accountSummaryError}}
<div class="text-red-500 p-4 bg-red-50 rounded">
    <p>Failed to load account summary. Please try refreshing the page or check if you're logged in.</p>
</div>
{{/accountSummaryError}}

{{#leaseListError}}
<tr class="bg-white text-center">
    <td colspan="4" class="p-4 text-red-500">Failed to load leases. Please try refreshing the page or check if you're logged in.</td>
</tr>
{{/leaseListError}}

{{! Login error message }}
{{#loginError}}
<div class="text-red-500">{{.}}</div>
{{/loginError}}

{{! Signup error message }}
{{#signupError}}
<div class="text-red-500">{{.}}</div>
{{/signupError}}

{{! OAuth error message }}
{{#oauthError}}
<div class="text-red-500">{{.}}</div>
{{/oauthError}}

{{! Toast notifications }}
{{#successToast}}
<div class="fixed bottom-20 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg">{{message}}</div>
{{/successToast}}

{{#errorToast}}
<div class="fixed bottom-20 right-4 bg-red-500 text-white px-4 py-2 rounded shadow-lg">{{message}}</div>
{{/errorToast}}

{{! Loading indicators }}
{{#spinnerIcon}}
<span class="animate-spin">↻</span>
{{/spinnerIcon}}

{{#errors}}
<div class="rounded-md bg-red-50 p-4 mb-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="mdi mdi-alert-circle text-red-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">There were errors with your submission</h3>
            <div class="mt-2 text-sm text-red-700">
                <ul role="list" class="list-disc space-y-1 pl-5">
                    {{#errors}}
                    <li>{{.}}</li>
                    {{/errors}}
                </ul>
            </div>
        </div>
    </div>
</div>
{{/errors}}

{{#success}}
<div class="rounded-md bg-green-50 p-4 mb-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="mdi mdi-check-circle text-green-400"></i>
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-green-800">{{success}}</p>
        </div>
    </div>
</div>
{{/success}} 