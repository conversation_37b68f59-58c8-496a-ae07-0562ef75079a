{{#pagination}}
<div class="flex justify-center items-center gap-4 px-4 py-2 mt-4 bg-white border rounded-lg">
  <button class="pagination-btn prev p-2 border rounded-md {{^hasPrev}}opacity-50 cursor-not-allowed{{/hasPrev}}" {{^hasPrev}}disabled{{/hasPrev}}>
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
  </button>
  <span class="text-sm">{{currentPage}} of {{totalPages}}</span>
  <button class="pagination-btn next p-2 border rounded-md {{^hasNext}}opacity-50 cursor-not-allowed{{/hasNext}}" {{^hasNext}}disabled{{/hasNext}}>
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
    </svg>
  </button>
</div>
{{/pagination}}
