<!-- Duplicate File Modal -->
<div class="fixed inset-0 z-50">
  <!-- Dark overlay with click handler to close -->
  <div
    class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto"
    id="duplicate-file-overlay"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none p-4"
  >
    <div
      class="bg-white rounded-lg shadow-xl w-full max-w-[489px] h-[329px] pointer-events-auto relative flex flex-col"
      style="width: 489px; height: 329px; border-radius: 8px;"
    >
      <!-- Modal Header -->
      <div class="flex justify-between items-center p-8 pb-0">
        <h3 class="text-2xl font-semibold text-gray-900">Duplicate File Detected</h3>
        <button
          class="p-1 hover:bg-gray-100 rounded"
          id="duplicate-file-close"
        >
          <svg
            class="w-6 h-6 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Divider -->
      <div class="mx-8 mt-6 border-t border-gray-200"></div>

      <!-- Modal Body -->
      <div class="flex-1 px-8 py-8">
        <p class="text-gray-700 text-base leading-relaxed">A file with the same name already exists. Would you like to replace the existing file?</p>
      </div>

      <!-- Modal Footer -->
      <div class="px-8 pb-8 mb-8 flex justify-between">
        <button
          id="cancel-duplicate-file-btn"
          class="w-[145px] h-[44px] text-base font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"
          style="border-radius: 4px; padding: 12px 32px;"
        >
          Cancel
        </button>
        <button
          id="replace-file-btn"
          class="w-[145px] h-[44px] text-base font-medium text-white bg-black hover:bg-gray-800"
          style="border-radius: 4px; padding: 12px 32px;"
        >
          Replace
        </button>
      </div>
    </div>
  </div>
</div>
