{{#pagination}}
<div class="flex justify-between items-center gap-4 px-4 py-2 mt-4 bg-white border rounded-lg">
  <span class="text-sm text-gray-500">
    {{showingText}}
  </span>
  <div class="flex items-center gap-2">
    <button 
      class="pagination-btn prev p-2 border rounded-md {{^hasPreviousPage}}opacity-50 cursor-not-allowed{{/hasPreviousPage}}" 
      {{^hasPreviousPage}}disabled{{/hasPreviousPage}} 
      data-page="{{#hasPreviousPage}}{{currentPage}}{{/hasPreviousPage}}"
    >
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>
    
    <div class="hidden md:flex gap-1">
      {{#pages}}
        {{#type === 'page'}}
          <button 
            data-page="{{number}}" 
            class="pagination-btn page w-8 h-8 flex justify-center items-center {{#isCurrent}}bg-indigo-600 text-white{{/isCurrent}} {{^isCurrent}}border{{/isCurrent}} rounded-md"
          >
            {{number}}
          </button>
        {{/type === 'page'}}
        {{#type === 'ellipsis'}}
          <span class="w-8 h-8 flex justify-center items-center">...</span>
        {{/type === 'ellipsis'}}
      {{/pages}}
    </div>
    
    <div class="flex md:hidden">
      <span class="text-sm">{{currentPage}} of {{totalPages}}</span>
    </div>
    
    <button 
      class="pagination-btn next p-2 border rounded-md {{^hasNextPage}}opacity-50 cursor-not-allowed{{/hasNextPage}}" 
      {{^hasNextPage}}disabled{{/hasNextPage}}
      data-page="{{#hasNextPage}}{{currentPage}}{{/hasNextPage}}"
    >
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>
  </div>
</div>
{{/pagination}} 