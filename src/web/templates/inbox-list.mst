{{#messages}}
<div class="bg-white shadow overflow-hidden sm:rounded-md">
    <ul role="list" class="divide-y divide-gray-200">
        {{#each messages}}
        <li>
            <div class="px-4 py-4 sm:px-6 hover:bg-gray-50 cursor-pointer"
                 hx-get="/api/messages/{{id}}"
                 hx-target="#modal"
                 hx-trigger="click">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            {{#if isRead}}
                            <i class="mdi mdi-email-open text-gray-400"></i>
                            {{else}}
                            <i class="mdi mdi-email text-indigo-600"></i>
                            {{/if}}
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-900">{{subject}}</p>
                            <p class="text-sm text-gray-500">{{from}}</p>
                        </div>
                    </div>
                    <div class="ml-2 flex-shrink-0 flex">
                        <p class="text-sm text-gray-500">{{date}}</p>
                    </div>
                </div>
            </div>
        </li>
        {{/each}}
    </ul>
</div>

{{#if pagination}}
<div class="mt-4 flex items-center justify-between">
    <div class="flex-1 flex justify-between sm:hidden">
        {{#if pagination.hasPrev}}
        <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                hx-get="/api/messages/prev?page={{pagination.prevPage}}"
                hx-target="#message-list">
            Previous
        </button>
        {{/if}}
        {{#if pagination.hasNext}}
        <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                hx-get="/api/messages/next?page={{pagination.nextPage}}"
                hx-target="#message-list">
            Next
        </button>
        {{/if}}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{pagination.start}}</span> to <span class="font-medium">{{pagination.end}}</span> of <span class="font-medium">{{pagination.total}}</span> results
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {{#if pagination.hasPrev}}
                <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                        hx-get="/api/messages/prev?page={{pagination.prevPage}}"
                        hx-target="#message-list">
                    <span class="sr-only">Previous</span>
                    <i class="mdi mdi-chevron-left"></i>
                </button>
                {{/if}}
                {{#if pagination.hasNext}}
                <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                        hx-get="/api/messages/next?page={{pagination.nextPage}}"
                        hx-target="#message-list">
                    <span class="sr-only">Next</span>
                    <i class="mdi mdi-chevron-right"></i>
                </button>
                {{/if}}
            </nav>
        </div>
    </div>
</div>
{{/if}}
{{/messages}} 