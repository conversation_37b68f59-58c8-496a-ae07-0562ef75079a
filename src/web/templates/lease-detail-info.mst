<!-- Lease Details Section -->
<div class="flex flex-1 flex-wrap">
  <h2 class="text-lg font-bold w-full mb-4">Lease Details</h2>

  <label class="mb-2 w-full" for="property-name" label="Property Name">Property Name</label>
  <input
    class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-gray-100"
    list="test"
    id="property-name"
    placeholder="Select a property"
    value="{{propertyName}}"
    readonly
  />
  <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
    <div class="flex flex-wrap md:w-1/2 w-full">
      <label class="w-full mb-2" for="unit">Rental Unit</label>
      <input
        class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-gray-100"
        id="unit"
        placeholder="Default Unit"
        value="{{roomName}}"
        readonly
      />
    </div>
    <div class="flex flex-wrap md:w-1/2 w-full">
      <label class="w-full mb-2" for="rent-due">Rent Due Date</label>
      <div class="relative w-full">
        <div
          class="flex items-center py-4 px-2 w-full border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 cursor-text"
          id="rent-due-container"
        >
          <input
            class="w-12 text-right focus:outline-none bg-transparent"
            id="rent-due-day"
            type="number"
            min="1"
            max="31"
            value="{{rentDueDay}}"
            placeholder="1"
          />
          <span id="rent-due-suffix" class="text-gray-700 ml-1 pointer-events-none">st of every month</span>
          <span id="rent-due-hint" class="text-gray-500 ml-2 text-sm pointer-events-none"></span>
        </div>
      </div>
    </div>
  </div>
  <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
    <div class="flex flex-wrap md:w-1/2 w-full">
      <label class="w-full mb-2" for="monthly-rent">Monthly Rent</label>
      <div class="relative w-full">
        <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">$</span>
        <input
          type="text"
          id="monthly-rent"
          placeholder="1"
          value="{{rentAmount}}"
          class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-white min-h-[44px]"
        />
      </div>
    </div>
    <div class="flex flex-wrap md:w-1/2 w-full">
      <label class="w-full mb-2" for="additional-fees">Extra Monthly Fees</label>
      <div class="relative w-full">
        <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">$</span>
        <input
          type="text"
          id="additional-fees"
          placeholder="0"
          value="{{additionalMonthlyFees}}"
          class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-white min-h-[44px]"
        />
      </div>
    </div>
  </div>
  <label class="mt-4 mb-2 w-full" for="lease-status">Lease Status</label>
  <select
    class="p-2 w-full border border-gray-300 rounded-md focus:outline-none bg-white h-[52px]"
    id="lease-status"
  >
    <option value="active" {{#status_active}}selected{{/status_active}}>Active</option>
    <option value="ended" {{#status_ended}}selected{{/status_ended}}>Ended</option>
  </select>
</div> 