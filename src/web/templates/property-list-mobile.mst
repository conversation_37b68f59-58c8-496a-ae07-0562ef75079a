{{#properties}}
<div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
  <a href="../property_detail/?id={{id}}">
    <div class="flex flex-wrap p-4 bg-white cursor-pointer group hover:bg-slate-50 {{#isInactive}}text-gray-400{{/isInactive}}">
      <div class="flex items-center w-full mb-2">
        {{#isInactive}}
        <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8V7a4 4 0 00-8 0v4" />
        </svg>
        {{/isInactive}}
        <span class="font-medium">{{name}}</span>
      </div>
      <div class="flex justify-between w-full">
        <span>Units Owned: {{totalRooms}}</span>
        <span>Balance: ${{owingBalance}}</span>
      </div>
      <span>Vacant Units: {{vacantRooms}}</span>
    </div>
  </a>
</div>
{{/properties}}
{{^properties}}
<div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
  <div class="flex flex-wrap p-4 bg-white">
    <span class="font-medium w-full mb-2 text-center text-gray-500">No properties found</span>
  </div>
</div>
{{/properties}}
