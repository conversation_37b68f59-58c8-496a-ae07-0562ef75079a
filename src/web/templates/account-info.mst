{{! Account Information Template }}

{{! Name Section }}
<div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
    <h3 class="font-semibold w-full md:w-1/4">Name</h3>
    <div class="w-full md:w-1/2">
        <p class="name-display">{{firstName}} {{lastName}}</p>
        <div class="name-edit hidden">
            <div class="mb-2">
                <input type="text" id="new-first-name-input" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="First Name" value="{{firstName}}">
                <div id="first-name-error" class="text-red-500 text-xs mt-1 hidden"></div>
            </div>
            <div>
                <input type="text" id="new-last-name-input" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Last Name" value="{{lastName}}">
                <div id="last-name-error" class="text-red-500 text-xs mt-1 hidden"></div>
            </div>
        </div>
    </div>
    <div class="w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0">
        <button 
            class="font-bold text-blue-500 hover:underline name-edit-btn"
            id="change-name-btn"
        >
            Update
        </button>
        <div class="name-edit-btns hidden">
            <button class="font-bold text-blue-500 hover:underline mr-2" id="confirm-name-update">Save</button>
            <button class="font-bold text-blue-500 hover:underline" id="cancel-name-update">Cancel</button>
        </div>
    </div>
</div>

{{! Email Section }}
<div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
    <h3 class="font-semibold w-full md:w-1/4">Email</h3>
    <div class="w-full md:w-1/2 text-left">
        <p>{{email}}</p>
    </div>
    <div class="w-full md:w-1/5"></div>
</div>

{{! Address Section }}
<div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
    <h3 class="font-semibold w-full md:w-1/4">Current Address</h3>
    <div class="w-full md:w-1/2">
        <div class="address-display">
            {{#address}}
            <p>{{address}}</p>
            {{/address}}
            {{^address}}
            <p>No address provided</p>
            {{/address}}
        </div>
        <div class="address-edit hidden">
            <input type="text" id="new-street" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 mb-2" placeholder="Street Address" value="{{addressStreet}}">
            <input type="text" id="new-unit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 mb-2" placeholder="Unit Number" value="{{addressUnit}}">
            <input type="text" id="new-city" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 mb-2" placeholder="City" value="{{addressCity}}">
            <select id="new-province" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 mb-2">
                <option value="">Select Province</option>
                <option value="AB" {{#isAB}}selected{{/isAB}}>Alberta</option>
                <option value="BC" {{#isBC}}selected{{/isBC}}>British Columbia</option>
                <option value="MB" {{#isMB}}selected{{/isMB}}>Manitoba</option>
                <option value="NB" {{#isNB}}selected{{/isNB}}>New Brunswick</option>
                <option value="NL" {{#isNL}}selected{{/isNL}}>Newfoundland and Labrador</option>
                <option value="NS" {{#isNS}}selected{{/isNS}}>Nova Scotia</option>
                <option value="ON" {{#isON}}selected{{/isON}}>Ontario</option>
                <option value="PE" {{#isPE}}selected{{/isPE}}>Prince Edward Island</option>
                <option value="QC" {{#isQC}}selected{{/isQC}}>Quebec</option>
                <option value="SK" {{#isSK}}selected{{/isSK}}>Saskatchewan</option>
                <option value="NT" {{#isNT}}selected{{/isNT}}>Northwest Territories</option>
                <option value="NU" {{#isNU}}selected{{/isNU}}>Nunavut</option>
                <option value="YT" {{#isYT}}selected{{/isYT}}>Yukon</option>
            </select>
            <input type="text" id="new-country" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 mb-2 bg-gray-100" placeholder="Country" value="Canada" readonly>
            <input type="text" id="new-postal-code" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Postal Code" value="{{addressZipCode}}">
        </div>
    </div>
    <div class="w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0">
        <button 
            class="font-bold text-blue-500 hover:underline address-edit-btn"
            id="change-address-btn"
        >
            Update
        </button>
        <div class="address-edit-btns hidden">
            <button class="font-bold text-blue-500 hover:underline mr-2" id="confirm-address-update">Save</button>
            <button class="font-bold text-blue-500 hover:underline" id="cancel-address-update">Cancel</button>
        </div>
    </div>
</div>

{{! Phone Section }}
<div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
    <h3 class="font-semibold w-full md:w-1/4">Phone Number</h3>
    <div class="w-full md:w-1/2">
        <div class="phone-display">
            {{#phoneNumber}}
            <p>{{phoneNumber}}</p>
            {{/phoneNumber}}
            {{^phoneNumber}}
            <p>No phone number provided</p>
            {{/phoneNumber}}
        </div>
        <div class="phone-edit hidden">
            <input type="tel" id="new-phone-input" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" value="{{phoneNumber}}">
        </div>
    </div>
    <div class="w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0">
        <button 
            class="font-bold text-blue-500 hover:underline phone-edit-btn"
            id="change-phone-btn"
        >
            Update
        </button>
        <div class="phone-edit-btns hidden">
            <button class="font-bold text-blue-500 hover:underline mr-2" id="confirm-phone-update">Save</button>
            <button class="font-bold text-blue-500 hover:underline" id="cancel-phone-update">Cancel</button>
        </div>
    </div>
</div>

<!-- Apply Referral Code Section -->
<div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
    <h3 class="font-semibold w-full md:w-1/4">Apply Referral Code</h3>
    <div class="w-full md:w-1/2">
        <div class="flex flex-row gap-2 items-center">
            <input id="apply-referral-code-input" type="text" class="w-full border border-gray-300 rounded px-2 py-1" placeholder="Type here" />
        </div>
        <p id="apply-referral-code-success" class="text-green-600 hidden mt-2 text-xs">Code applied successfully</p>
        <p id="apply-referral-code-fail" class="text-red-600 hidden mt-2 text-xs">Failed to apply code</p>
    </div>
    <div class="w-full md:w-1/5 flex flex-col items-end mt-2 md:mt-0">
        <button id="apply-referral-code-btn" class="bg-slate-800 text-white px-4 py-2 rounded text-sm">Apply</button>
    </div>
</div>

{{! View Type Section }}
<div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
    <h3 class="font-semibold w-full md:w-1/4">Account View</h3>
    <div class="w-full md:w-1/2">
        <p>Current View: {{viewType}}</p>
    </div>
    <div class="w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0">
        <button 
            class="bg-slate-800 text-white px-4 py-2 rounded text-sm"
            id="change-view-btn"
        >
            Switch to {{switchToView}}
        </button>
    </div>
</div>

{{! Password Section }}
<div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
    <h3 class="font-semibold w-full md:w-1/4">Password</h3>
    <div class="w-full md:w-1/2">
        <div class="password-display">
            <p>*******</p>
        </div>
        <div class="password-edit hidden">
            <input type="password" id="current-password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 mb-2" placeholder="Current Password">
            <input type="password" id="new-password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 mb-2" placeholder="New Password">
            <input type="password" id="confirm-password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Confirm New Password">
        </div>
    </div>
    <div class="w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0">
        <button 
            class="font-bold text-blue-500 hover:underline password-edit-btn"
            id="change-password-btn"
        >
            Change
        </button>
        <div class="password-edit-btns hidden">
            <button class="font-bold text-blue-500 hover:underline mr-2" id="confirm-password-update">Save</button>
            <button class="font-bold text-blue-500 hover:underline" id="cancel-password-update">Cancel</button>
        </div>
    </div>
</div>

{{! Account Status Section }}
<div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
    <h3 class="font-semibold w-full md:w-1/4">Account Status</h3>
    <div class="w-full md:w-1/2">
        <p>{{accountStatus}}</p>
    </div>
    {{^isVerified}}
    <button 
        class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0"
        id="start-verification-btn"
    >
        Start Verification
    </button>
    {{/isVerified}}
    <div class="w-full md:w-1/5"></div>
</div>