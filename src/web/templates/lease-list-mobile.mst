{{#propertyGroups}}
<!-- Property Group -->
<div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
  <div class="flex flex-wrap p-4 bg-slate-200 cursor-pointer group property-group-mobile" data-property-name="{{propertyName}}">
    <span class="flex items-center gap-2 font-medium w-full truncate">
      <svg class="group-arrow w-5 h-5 text-gray-500 transform flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
      <span class="truncate">{{propertyName}}</span>
    </span>
  </div>
  
  <!-- Lease Details Container -->
  <div class="lease-details-mobile">
    {{#leases}}
    <div class="flex flex-wrap p-4 bg-white border-t hover:bg-slate-50 cursor-pointer" onclick="window.location.href='../lease_detail/?id={{id}}'">
      <div class="flex items-center gap-2 mb-2 w-full">
        {{#isLeaseActive}}
        <img src="/assets/user.svg" class="w-5 h-5 text-gray-500" alt="">
        {{/isLeaseActive}}
        {{^isLeaseActive}}
        <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h8a2 2 0 002-2V8m-9 4h4" />
        </svg>
        {{/isLeaseActive}}
        <span class="font-medium">{{tenantName}}</span>
      </div>
      <div class="flex justify-between w-full text-sm">
        <span>Room: {{roomName}}</span>
        <span class="{{#owingBalance}}{{#isPositive}}text-red-500{{/isPositive}}{{/owingBalance}}">Balance: ${{owingBalance}}</span>
      </div>
      <div class="flex justify-between w-full text-sm">
        <span>Rent Report: {{#isActive}}On{{/isActive}}{{^isActive}}Off{{/isActive}}</span>
        <span>Last Payment: {{lastPaymentDate}}</span>
      </div>
    </div>
    {{/leases}}
  </div>
</div>
{{/propertyGroups}}
{{^propertyGroups}}
<div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
  <div class="flex flex-wrap p-4 bg-white">
    <span class="font-medium w-full mb-2 text-center text-gray-500">No leases found</span>
  </div>
</div>
{{/propertyGroups}}
