<div class="fixed inset-0 z-50">
  <!-- 带有点击处理程序的暗色遮罩层 -->
  <div
    class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto"
    onclick="document.getElementById('modal').innerHTML = ''"
  ></div>

  <!-- 模态框容器 -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
    >
      <!-- 模态框头部 -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Unit Details</h3>
          <p>View or update unit information</p>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          id="close-modal"
          onclick="document.getElementById('modal').innerHTML = ''"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- 模态框主体 -->
      <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
        <form>
          <div class="flex flex-wrap">
            <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="room-name">Unit Name *</label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="room-name"
                  placeholder="Unit name"
                  value="{{name}}"
                  required
                />
              </div>
            </div>
            
            <div class="w-full mt-8">
              <label class="w-full" for="room-notes">
                <h2 class="text-lg font-semibold text-gray-900">Unit Notes</h2>
                <p class="text-sm text-gray-500 mb-4">
                  A place to keep notes for internal purposes. Visible to only
                  you and your team.
                </p>
              </label>
              <textarea
                class="p-4 w-full border border-gray-300 rounded-md focus:outline-none h-48 resize-none"
                id="room-notes"
                placeholder="Add notes about the unit here"
              >{{notes}}</textarea>

              <!-- Delete Room Section -->
              <div class="mt-8 mb-16">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Delete Room</h3>
                <button
                  id="delete-room"
                  data-room-id="{{id}}"
                  class="px-4 py-2 text-sm font-medium text-red-500 bg-red-50 border border-red-500 rounded-md hover:bg-red-100"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- 模态框底部 -->
      <div
        class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          onclick="document.getElementById('modal').innerHTML = ''"
        >
          Cancel
        </button>
        <button
          id="update-room"
          data-room-id="{{id}}"
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Update
        </button>
      </div>
    </div>
  </div>
</div> 