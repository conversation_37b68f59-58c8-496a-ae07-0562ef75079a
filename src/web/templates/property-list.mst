{{#properties}}
<tr class="bg-white hover:bg-slate-100 text-left group cursor-pointer border-t {{#isInactive}}text-gray-400{{/isInactive}}" data-property-id="{{id}}">
  <td colspan="4" class="p-0">
    <a href="../property_detail/?id={{id}}" class="block">
      <div class="flex w-full">
        <div class="p-4 w-3/12">
          <div class="flex items-center">
            {{#isInactive}}
            <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8V7a4 4 0 00-8 0v4" />
            </svg>
            {{/isInactive}}
            <span class="font-medium">{{name}}</span>
          </div>
        </div>
        <div class="p-4 flex-1">{{totalRooms}}</div>
        <div class="p-4 flex-1">{{vacantRooms}}</div>
        <div class="p-4 flex-1">${{owingBalance}}</div>
      </div>
    </a>
  </td>
</tr>
{{/properties}}
{{^properties}}
<tr class="bg-white text-center">
  <td colspan="4" class="p-4 text-gray-500">No properties found</td>
</tr>
{{/properties}} 