// User API methods
async function getUserInfo() {
    const response = await fetch('/v1/user', {
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
    });

    if (!response.ok) {
        throw new Error('Failed to get user info');
    }

    return response.json();
}

async function updateUser(updates) {
    const response = await fetch('/v1/user', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify(updates)
    });

    if (!response.ok) {
        throw new Error('Failed to update user');
    }

    return response.json();
}

async function updatePassword(currentPassword, newPassword) {
    const response = await fetch('/v1/user/password', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({
            currentPassword,
            newPassword
        })
    });

    if (!response.ok) {
        throw new Error('Failed to update password');
    }

    return response.json();
}

async function startVerification() {
    const response = await fetch('/v1/user/verify', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
    });

    if (!response.ok) {
        throw new Error('Failed to start verification');
    }

    return response.json();
}

async function closeAccount() {
    const response = await fetch('/v1/user', {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
    });

    if (!response.ok) {
        throw new Error('Failed to close account');
    }

    return response.json();
}

// Update user's view type
async function updateViewType(viewType) {
    const response = await fetch('/v1/user/view-type', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({ viewType })
    });

    if (!response.ok) {
        throw new Error('Failed to update view type');
    }

    return response.json();
}

// Get user's payment info
async function getPaymentInfo() {
    const response = await fetch('/v1/user/payinfo', {
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
    });

    if (!response.ok) {
        throw new Error('Failed to get payment info');
    }

    return response.json();
}

export const userApi = {
    getUserInfo,
    updateUser,
    updatePassword,
    startVerification,
    closeAccount,
    updateViewType,
    getPaymentInfo
}; 