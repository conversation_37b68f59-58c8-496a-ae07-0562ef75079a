import Mustache from 'mustache';

export async function renderSidebarNav(userData) {
    const isLandlord = (userData.viewType || 'landlord') === 'landlord';
    const navbarResponse = await fetch('/partials/navbar.mst');
    const navbarTemplate = await navbarResponse.text();
    const navbarHtml = Mustache.render(navbarTemplate, {
        baseUrl: window.config.baseUrl,
        isLandlord: isLandlord
    });
    // 只替换侧边栏
    const $sidebar = document.getElementById('sidebar-nav');
    if ($sidebar) {
        $sidebar.outerHTML = navbarHtml;
    }
} 