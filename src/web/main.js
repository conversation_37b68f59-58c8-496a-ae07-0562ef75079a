// 导入全局依赖
import 'htmx.org'
import jQuery from 'jquery'

// 设置全局变量
window.$ = window.jQuery = jQuery;

// Set up global config
window.config = window.config || {};
window.config.baseUrl = window.config.baseUrl || '';

// Mobile navbar drawer function
function openDrawer() {
  const drawer = document.getElementById("mobile-nav")

  if (drawer.classList.contains("h-0")) {
    drawer.classList.add("h-screen")
    drawer.classList.remove("h-0")
  } else {
    drawer.classList.add("h-0")
    drawer.classList.remove("h-screen")
  }
}

// Make openDrawer function globally available
window.openDrawer = openDrawer;