.report-problem-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-problem-container h1 {
    margin-bottom: 2rem;
    color: #333;
    font-size: 1.8rem;
}

.report-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #555;
}

.form-group select,
.form-group input,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.char-count {
    text-align: right;
    color: #666;
    font-size: 0.875rem;
}

.file-upload {
    border: 2px dashed #ddd;
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
}

.upload-hint {
    margin-top: 0.5rem;
    color: #666;
    font-size: 0.875rem;
}

.form-actions {
    margin-top: 1rem;
}

.btn-primary {
    background-color: #007bff;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-primary:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* 错误状态样式 */
.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: #dc3545;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .report-problem-container {
        margin: 1rem;
        padding: 1rem;
    }

    .report-problem-container h1 {
        font-size: 1.5rem;
    }
} 