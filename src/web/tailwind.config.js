/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./pages/**/*.{html,js}",
    "./partials/**/*.{html,mst}",
    "./api/**/*.html",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["Segoe UI", "-apple-system", "BlinkMacSystemFont", "system-ui", "sans-serif"],
      },
      colors: {
        rmred: {
          DEFAULT: "#ea1a1a",
          light: "#fca5a5",
          dark: "#b91c1c",
        },
        rmgray: {
          DEFAULT: "#323336",
          light: "#525359",
        },
      },
      borderRadius: {
        '4xl': '2rem',
      },
    },
  },
  plugins: [require("daisyui")],
  daisyui: {
    themes: ["light"], // Only use light theme, disable dark mode
  },
}

