<!-- Name Update Modal -->
<div id="name-update-modal" class="hidden">
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Update Name</h3>
                <div class="mt-2 px-7 py-3">
                    <input type="text" id="new-name-input" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Enter new name">
                </div>
                <div class="flex justify-end gap-4 mt-4">
                    <button class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300" onclick="closeModal()">Cancel</button>
                    <button class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600" id="confirm-name-update">Update</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Address Update Modal -->
<div id="address-update-modal" class="hidden">
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Update Address</h3>
                <div class="mt-2 px-7 py-3 space-y-4">
                    <input type="text" id="new-street" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Street Address">
                    <input type="text" id="new-unit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Unit Number">
                    <input type="text" id="new-city" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="City">
                    <input type="text" id="new-province" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Province">
                    <input type="text" id="new-country" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Country">
                    <input type="text" id="new-postal-code" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Postal Code">
                </div>
                <div class="flex justify-end gap-4 mt-4">
                    <button class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300" onclick="closeModal()">Cancel</button>
                    <button class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600" id="confirm-address-update">Update</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Phone Update Modal -->
<div id="phone-update-modal" class="hidden">
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Update Phone Number</h3>
                <div class="mt-2 px-7 py-3">
                    <input type="tel" id="new-phone-input" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="Enter new phone number">
                </div>
                <div class="flex justify-end gap-4 mt-4">
                    <button class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300" onclick="closeModal()">Cancel</button>
                    <button class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600" id="confirm-phone-update">Update</button>
                </div>
            </div>
        </div>
    </div>
</div> 