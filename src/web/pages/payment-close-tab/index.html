<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success-icon {
            color: #22c55e;
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .message {
            color: #374151;
            margin-bottom: 1rem;
        }
        .closing-message {
            color: #6b7280;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <h2 class="message">Payment Successful!</h2>
        <p class="closing-message">This tab will close automatically...</p>
    </div>

    <script>
        // 标记支付完成，通知父窗口刷新
        try {
            // 使用localStorage通知其他标签页支付已完成
            localStorage.setItem('paymentCompleted', Date.now().toString());

            // 如果有opener窗口，尝试通知它刷新
            if (window.opener && !window.opener.closed) {
                try {
                    // 发送消息给父窗口
                    window.opener.postMessage({ type: 'PAYMENT_SUCCESS' }, window.location.origin);
                } catch (e) {
                    console.log('Could not notify parent window:', e);
                }
            }
        } catch (e) {
            console.log('Could not set payment completion flag:', e);
        }

        setTimeout(() => {
            window.close();
        }, 2000);

        setTimeout(() => {
            if (!window.closed) {
                document.querySelector('.closing-message').textContent = 'Please close this tab manually.';
            }
        }, 3000);
    </script>
</body>
</html>
