<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success-icon {
            color: #22c55e;
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .message {
            color: #374151;
            margin-bottom: 1rem;
        }
        .closing-message {
            color: #6b7280;
            font-size: 0.9rem;
        }
        .close-button {
            background-color: #22c55e;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 1rem;
            font-size: 0.9rem;
        }
        .close-button:hover {
            background-color: #16a34a;
        }
        .return-button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 0.5rem;
            margin-left: 0.5rem;
            font-size: 0.9rem;
        }
        .return-button:hover {
            background-color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <h2 class="message">Payment Successful!</h2>
        <p class="closing-message" id="closing-message">This tab will close automatically...</p>
        <div id="manual-controls" style="display: none;">
            <button class="close-button" onclick="closeTab()">Close Tab</button>
            <button class="return-button" onclick="returnToAccount()">Return to Account</button>
        </div>
    </div>

    <script>
        function notifyPaymentSuccess() {
            try {
                localStorage.setItem('paymentCompleted', Date.now().toString());
                console.log('Payment completion flag set in localStorage');
                if (window.opener && !window.opener.closed) {
                    try {
                        window.opener.postMessage({ type: 'PAYMENT_SUCCESS' }, window.location.origin);
                        console.log('Payment success message sent to parent window');
                    } catch (e) {
                        console.log('Could not notify parent window:', e);
                    }
                }
            } catch (e) {
                console.log('Could not set payment completion flag:', e);
            }
        }
        function closeTab() {
            notifyPaymentSuccess();
            try {
                window.close();
                setTimeout(() => {
                    if (!window.closed) {
                        showManualControls();
                    }
                }, 500);
            } catch (e) {
                console.log('Could not close window:', e);
                showManualControls();
            }
        }
        function returnToAccount() {
            notifyPaymentSuccess();
            try {
                if (window.opener && !window.opener.closed) {
                    window.opener.location.href = window.location.origin + '/pages/account/#Plans';
                    window.close();
                } else {
                    window.location.href = window.location.origin + '/pages/account/#Plans';
                }
            } catch (e) {
                console.log('Could not navigate:', e);
                window.location.href = window.location.origin + '/pages/account/#Plans';
            }
        }
        function showManualControls() {
            document.getElementById('closing-message').textContent = 'Tab could not be closed automatically.';
            document.getElementById('manual-controls').style.display = 'block';
        }
        window.addEventListener('load', function() {
            notifyPaymentSuccess();
            setTimeout(() => {
                closeTab();
            }, 2000);
            setTimeout(() => {
                if (!window.closed) {
                    showManualControls();
                }
            }, 5000);
        });
    </script>
</body>
</html>
