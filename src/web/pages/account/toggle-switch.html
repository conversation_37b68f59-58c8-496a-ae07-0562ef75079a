<label class="relative inline-flex items-center cursor-pointer">
  <input
    type="checkbox"
    class="sr-only peer"
    hx-post="/api/toggle-feature"
    hx-trigger="click"
  />
  <div
    class="w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-slate-300 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:bg-slate-800 after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all"
  ></div>
  <span class="sr-only">Toggle feature</span>
</label>
