<div class="fixed inset-0 z-50">
  <div class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto" onclick="document.getElementById('modal').innerHTML = ''"></div>
  <div class="fixed inset-0 flex items-center justify-center pointer-events-none">
    <div class="bg-white sm:rounded-lg shadow-xl sm:w-8/12 w-full h-full sm:max-h-[80vh] sm:min-h-[60vh] overflow-hidden pointer-events-auto relative">
      <div class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center">
        <div>
          <h3 class="text-xl font-semibold text-gray-900">Add Lease</h3>
          <p class="text-sm text-gray-600">Add information about your current lease</p>
        </div>
        <button class="p-2 hover:bg-gray-100 rounded-full" onclick="document.getElementById('modal').innerHTML = ''">
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div class="px-6 py-8 overflow-auto" style="height: calc(100% - 120px)">
        <form>
          <h2 class="text-2xl font-semibold mb-6">Lease Information</h2>
          <div class="mb-6">
            <label class="block text-base font-semibold mb-2" for="lease-type">Is this a current or former lease?</label>
            <select id="lease-type" class="w-full border border-gray-300 rounded px-4 py-4 text-base">
              <option value="former">Former</option>
              <option value="current" selected>Current</option>
            </select>
          </div>
          <div class="mb-6">
            <label class="block text-base font-semibold mb-2" for="monthly-rent">How much was your monthly rent?</label>
            <div class="relative w-full">
              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">$</span>
              <input id="monthly-rent" type="number" min="0" class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none" />
            </div>
          </div>
          <div class="mb-6">
            <label class="block text-base font-semibold mb-2">How long was your tenancy?</label>
            <div class="flex flex-wrap gap-4">
              <div class="flex-1 min-w-[200px]">
                <label class="block mb-2" for="lease-from">From:</label>
                <input id="lease-from" type="date" class="w-full border border-gray-300 rounded px-4 py-4 text-base" />
              </div>
              <div class="flex-1 min-w-[200px]">
                <label class="block mb-2" for="lease-to">To:</label>
                <input id="lease-to" type="date" class="w-full border border-gray-300 rounded px-4 py-4 text-base" />
                <input id="lease-to-present" type="text" class="w-full border border-gray-200 rounded px-4 py-4 text-base bg-gray-100 text-gray-500" value="Present" readonly style="display:none;" />
              </div>
            </div>
            <div id="lease-date-error" class="text-red-500 text-sm mt-2 hidden">Start date must be before end date.</div>
          </div>
          <div class="mb-6 flex items-center" id="current-tenant-checkbox-row">
            <input id="is-current-tenant" type="checkbox" class="mr-2" />
            <label for="is-current-tenant" class="text-base">I am currently a tenant here</label>
          </div>
        </form>
      </div>
      <div class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t">
        <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" onclick="document.getElementById('modal').innerHTML = ''">Cancel</button>
        <button id="lease-info-continue-btn" class="px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-gray-800">Continue</button>
      </div>
    </div>
  </div>
</div> 