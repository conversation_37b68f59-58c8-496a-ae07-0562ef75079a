{{> landing-header }}
<body class="min-h-screen bg-white">
    <!-- Header -->
    {{> landing-navbar }}

    <!-- Main content-->
    <main class="flex flex-col items-center min-h-screen w-full bg-white px-8">
      <div class="flex flex-col items-center mt-16 gap-8 w-full md:w-[552px]">
        <h2 class="font-bold text-2xl">Reset Password</h2>
        
        <!-- Step 1: Email Input -->
        <div id="step1" class="flex flex-col gap-8 w-full">
          <p>Enter your email address to receive a verification code</p>
          
          <!-- Error Message -->
          <p class="text-red-500 hidden" id="errorMessage">Invalid email address</p>
          
          <div class="flex flex-col gap-4 w-full">
            <label for="email" class="font-medium">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Enter your email"
            />
          </div>
          
          <button
            id="sendCodeBtn"
            class="w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
          >
            Send Verification Code
          </button>
          
          <a
            href="{{baseUrl}}/pages/login/"
            class="text-center text-rmred hover:text-rmred-dark font-medium underline"
          >
            Return to login
          </a>
        </div>

        <!-- Step 2: Verification Code and New Password -->
        <div id="step2" class="flex flex-col gap-8 w-full hidden">
          <p>
            A 6 digit code has been sent to
            <span class="font-bold" id="userEmail"></span>
          </p>
          
          <!-- Error Message -->
          <p class="text-red-500 hidden" id="verificationError">Invalid verification code</p>
          
          <div class="flex flex-col gap-4 w-full">
            <label for="verificationCode" class="font-medium">Verification code</label>
            <input
              type="text"
              id="verificationCode"
              name="verificationCode"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="0 0 0 0 0 0"
              maxlength="6"
            />
          </div>

          <div class="flex flex-col gap-4 w-full">
            <label for="newPassword" class="font-medium">New Password</label>
            <input
              type="password"
              id="newPassword"
              name="newPassword"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Enter new password"
            />
          </div>

          <div class="flex flex-col gap-4 w-full">
            <label for="confirmPassword" class="font-medium">Confirm New Password</label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Re-enter new password"
            />
          </div>

          <div class="w-full">
            <button
              id="resendButton"
              class="font-bold text-rmred underline"
            >
              Resend verification
            </button>
          </div>

          <button
            id="resetPasswordBtn"
            class="w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
          >
            Reset Password
          </button>

          <a
            href="{{baseUrl}}/pages/login/"
            class="text-center text-rmred hover:text-rmred-dark font-medium underline"
          >
            Return to login
          </a>
        </div>
      </div>
    </main>

    {{> landing-footer}}
    <script type="module" src="./main.js"></script>
</body>
</rewritten_file> 