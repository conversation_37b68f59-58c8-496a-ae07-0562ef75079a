<div class="fixed inset-0 z-50">
  <div class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto" onclick="this.parentElement.remove()"></div>
  <div class="fixed inset-0 flex items-center justify-center pointer-events-none">
    <div class="bg-white sm:rounded-lg shadow-xl max-w-[600px] w-full h-fit overflow-hidden pointer-events-auto relative">
      <div class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900">Create Report</h3>
        <button class="p-2 hover:bg-gray-100 rounded-full" onclick="document.getElementById('modal').innerHTML = ''">
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      <div class="px-8 py-8">
        <h2 class="text-xl font-bold mb-2">Lease Selection</h2>
        <p class="text-sm mb-4">Find and select the lease you would like to make a report for.</p>
        <input
          id="lease-input"
          placeholder="Search for a name, email, phone number, or property"
          type="text"
          class="p-2 px-10 border border-1 rounded text-sm w-full"
        />
      </div>
      <div class="flex justify-between items-center px-8 py-4 bg-gray-50 border-t">
        <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          onclick="document.getElementById('modal').innerHTML = ''">Cancel</button>
        <button class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
          onclick="openReportStep2()">Continue</button>
      </div>
    </div>
  </div>
</div>
<script>
function openReportStep2() {
  const leaseValue = document.getElementById('lease-input').value;
  localStorage.setItem('selectedLeaseInfo', leaseValue);
  fetch('./modal-create-report-step2.html')
    .then(res => res.text())
    .then(html => {
      document.getElementById('modal').innerHTML = html;
    });
}
</script> 