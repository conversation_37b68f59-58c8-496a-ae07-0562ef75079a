<div class="fixed inset-0 z-50">
  <div class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto" onclick="this.parentElement.remove()"></div>
  <div class="fixed inset-0 flex items-center justify-center pointer-events-none">
    <div class="bg-white sm:rounded-lg shadow-xl max-w-[600px] w-full h-fit overflow-hidden pointer-events-auto relative">
      <div class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900">Create Report</h3>
        <button class="p-2 hover:bg-gray-100 rounded-full" onclick="document.getElementById('modal').innerHTML = ''">
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      <div class="px-8 py-8">
        <h2 class="text-xl font-bold mb-2">Report Details</h2>
        <div class="mb-4 text-sm">
          Creating a report for <span class="font-bold" id="lease-info"></span>
        </div>
        <form id="create-report-form" class="space-y-4">
          <div>
            <label class="block mb-1">Report Purpose</label>
            <select name="purpose" class="border rounded p-2 w-full">
              <option value="">Select Purpose</option>
              <option value="damage">Damage</option>
              <option value="complaint">Complaint</option>
              <option value="other">Other</option>
            </select>
          </div>
          <div>
            <label class="block mb-1">Report Visibility</label>
            <div class="flex gap-4">
              <label class="flex items-center gap-2">
                <input type="radio" name="visibility" value="private" checked>
                Private report
              </label>
              <label class="flex items-center gap-2">
                <input type="radio" name="visibility" value="public">
                Public report
              </label>
            </div>
          </div>
          <div>
            <label class="block mb-1">Report Details</label>
            <textarea name="details" class="w-full border rounded p-2" rows="4" placeholder="Complaint details goes in here,"></textarea>
          </div>
        </form>
      </div>
      <div class="flex justify-between items-center px-8 py-4 bg-gray-50 border-t">
        <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          onclick="openReportStep1()">Back</button>
        <button class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
          type="submit" form="create-report-form">Post</button>
      </div>
    </div>
  </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
  const leaseInfo = localStorage.getItem('selectedLeaseInfo') || '';
  document.getElementById('lease-info').textContent = leaseInfo;
});
function openReportStep1() {
  fetch('./modal-create-report-step1.html')
    .then(res => res.text())
    .then(html => {
      document.getElementById('modal').innerHTML = html;
    });
}
</script> 