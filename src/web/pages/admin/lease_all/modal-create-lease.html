<div class="fixed inset-0 z-50">
  <!-- Dark overlay with click handler to close -->
  <div
    class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto"
    onclick="document.getElementById('modal').innerHTML = ''"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Create Lease</h3>
          <p>Add a new lease to your property</p>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          onclick="document.getElementById('modal').innerHTML = ''"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
        <form>
          <div class="flex flex-wrap">
            <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="property-select">Property Name *</label>
                <select
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="property-select"
                  disabled
                >
                  <option value="">Loading properties...</option>
                </select>
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="room-select">Room *</label>
                <select
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="room-select"
                  disabled
                >
                  <option value="">Select a property first</option>
                </select>
              </div>
            </div>
            <div class="w-full flex gap-4 mt-4">
              <!-- Line 2-->
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="start-date">Move-In Date *</label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="start-date"
                  placeholder="Select a date"
                  type="date"
                  onclick="this.showPicker()"
                />
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="rent-amount">Monthly Rent *</label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="rent-amount"
                    placeholder="1"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  />
                </div>
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="additional-fees">Additional Monthly Fees</label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="additional-fees"
                    placeholder="0"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  />
                </div>
              </div>
            </div>
            <div class="w-full flex gap-4 mt-4">
              <!-- Line 3-->
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="key-deposit">Key Deposit</label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="key-deposit"
                    placeholder="0"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  />
                </div>
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="rent-deposit">Rent Deposit</label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="rent-deposit"
                    placeholder="0"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  />
                </div>
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="other-deposits">Other Deposits</label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="other-deposits"
                    placeholder="0"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  />
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Modal Footer -->
      <div
        class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          onclick="document.getElementById('modal').innerHTML = ''"
        >
          Cancel
        </button>
        <button
          id="create-lease-btn"
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Create Lease
        </button>
      </div>
    </div>
  </div>
</div> 