import fs from 'fs';
import path from 'path';
import Mustache from 'mustache';

/**
 * Custom Vite plugin for Mustache templates
 * @param {Object} options - Plugin options
 * @param {Object} options.data - Global data available to all templates
 * @param {string} options.partialDirectory - Directory containing partials
 * @returns {Object} Vite plugin
 */
export default function viteMustachePlugin(options = {}) {
  const { data = {}, partialDirectory = '' } = options;
  
  // Normalize baseUrl to not have trailing slash
  if (data.baseUrl && data.baseUrl.endsWith('/')) {
    data.baseUrl = data.baseUrl.slice(0, -1);
  }
  
  // Cache for partials
  const partials = {};
  
  // Load all partials at startup
  const loadPartials = () => {
    if (!partialDirectory || !fs.existsSync(partialDirectory)) return;
    
    try {
      const files = fs.readdirSync(partialDirectory);
      files.forEach(file => {
        if (file.endsWith('.mst')) {
          const partialName = path.basename(file, '.mst');
          const partialPath = path.join(partialDirectory, file);
          try {
            partials[partialName] = fs.readFileSync(partialPath, 'utf-8');
          } catch (error) {}
        }
      });
    } catch (error) {}
  };
  
  loadPartials();
  
  return {
    name: 'vite-plugin-mustache',
    
    transformIndexHtml: {
      order: 'pre',
      handler(html, ctx) {
        if (!ctx || !html) return html || '';

        try {
          if (process.env.NODE_ENV !== 'production') {
            loadPartials();
          }

          // Create a copy of data for this specific page
          const pageData = { ...data };

          // In development, use simple script path
          // In production, Vite will handle script injection automatically
          if (process.env.NODE_ENV !== 'production') {
            // Determine the script path based on the current page
            if (ctx.filename) {
              const filename = ctx.filename;

              // For blog pages: pages/blog/article-slug/index.html -> ./main.js
              const blogMatch = filename.match(/pages\/blog\/([^/]+)\/index\.html$/);
              if (blogMatch) {
                pageData.script = './main.js';
              }
              // For regular pages: pages/login/index.html -> ./main.js
              else {
                const pageMatch = filename.match(/pages\/([^/]+)\/index\.html$/);
                if (pageMatch) {
                  pageData.script = './main.js';
                }
              }
            }
          } else {
            // In production, remove the script placeholder - Vite will inject it
            pageData.script = '';
          }

          // First render partials with data
          const renderedPartials = {};
          for (const [name, template] of Object.entries(partials)) {
            renderedPartials[name] = Mustache.render(template, pageData);
          }

          // Then render main template
          let result = Mustache.render(html, pageData, renderedPartials);

          // Decode HTML entities in the result
          result = result.replace(/&#x2F;/g, '/');

          // Fix any double slashes in URLs (except for http:// or https://)
          result = result.replace(/([^:])\/+/g, '$1/');

          // Log only if baseUrl wasn't replaced
          if (result.includes('{{baseUrl}}')) {
          }

          return result;
        } catch (error) {
          return html;
        }
      }
    },
    
    configureServer(server) {
      server.watcher.add(path.join(partialDirectory, '*.mst'));
    },
    
    handleHotUpdate({ file, server }) {
      if (file.endsWith('.mst')) {
        loadPartials();
        server.ws.send({ type: 'full-reload' });
        return [];
      }
    }
  };
} 