# 🌐 多页面web项目

这是一个使用 Vite 构建的现代 Web 项目，结合 Mustache 和 HTMX 实现动态内容加载。

## 📁 项目结构

- **主页**: `index.html` - landing page
- **登录**: `pages/login/index.html` - 登录页面
- **注册**: `pages/signup/index.html` - 注册页面
- **房源**: `pages/property/index.html` - 房源页面
- **租赁**: `pages/lease/index.html` - 租赁页面

## ✨ 主要功能

- **动态加载**: 使用 HTMX 和 Ajax 加载新闻和产品。
- **响应式设计**: 使用 Tailwind CSS 实现。
- **模拟 API**: `mock-server.js` 提供模拟数据。

## 🚀 开发与构建

- **开发服务器**: `npm run dev` 启动 Vite 开发服务器。
- **构建**: `npm run build` 进行项目构建。

## 📦 依赖

- `handlebars` - 模板引擎
- `htmx.org` - 动态加载 
   - 从 https://unpkg.com/htmx.org 得到 min.js 文件存放项目 assets 目录
- `jquery` - DOM 操作
- `tailwindcss` - 样式设计

## 🛠️ 安装

1. 克隆项目:

2. 安装依赖:
   ```bash
   npm install
   ```

3. 启动开发服务器:
   ```bash
   npm run dev
   ```
4. 编译为静态文件
   ```bash
   npm run build
   ```

## 部署
1. 进入src/web目录, 
2. 方法1: 运行 npm run build, 然后移动dist文件夹到所需位置, 修改nginx的配置文件, 将静态文件指向 dist/index.html
3. 方法2: 不更改nginx配置, 使用过去的appweb配置:
将vite.config.js 的 BASE_URL 和 main.js 的 baseUrl 修改为 ’/web’, 然后运行 npm run build, 将dist目录下的所有文件放到原来appweb静态网页的位置, 即 cp -r dist/* /opt/appd9/appweb/src/webroot/public/web/
