package middleware

import (
	"net/http"
	"rent_report/auth"
	"rent_report/entities"
	"rent_report/utils"

	"github.com/gin-gonic/gin"
)

// RequireAdmin 检查用户是否是管理员
func RequireAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从token中获取用户ID
		userID, err := utils.GetUserIDFromToken(c.Request)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// 从数据库获取用户信息
		user, err := entities.GetUserByID(c.Request.Context(), userID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
			c.Abort()
			return
		}

		// 检查用户是否是管理员
		if user.Role != entities.RoleAdmin {
			c.JSON(http.StatusForbidden, gin.H{"error": "Forbidden"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// PermissionMiddleware checks if the user has permission to access the resource
func PermissionMiddleware(domain, object, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, err := utils.GetUserIDFromToken(c.Request)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// Get user from database
		user, err := entities.GetUserByID(c.Request.Context(), userID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
			c.Abort()
			return
		}

		// Check permission
		if !auth.CheckPermission(user.Role, domain, object, action) {
			c.JSON(http.StatusForbidden, gin.H{"error": "Forbidden"})
			c.Abort()
			return
		}

		c.Next()
	}
}
