# rent_report

## project structure

```
.
├── README.md
├── docs
│   ├── README.md
│   ├── change_requests
│   │   ├── YYYYMMDD-[branch-name]-[change-request-title].md # every change needs a request document
│   ├── README.md # naming convention, documentation, and other useful information
├── build
│   ├── assets
│   │   └── favicon.ico
│   └── bin
├── configs
│   └── app.ini
├── design # frontend design mockup for main content. (landing page design see https://github.com/real-rm/rent_report_design )
│   ├── README.md
│   ├── src
│   │   ├── assets
│   │   ├── components
│   ├── package.json
│   ├── package-lock.json
├── src
│   ├── web # frontend source code for main content
│   ├── README.md
│   ├── lib
│   ├── controller
│   │   ├── README.md
│   │   ├── controller.go
│   │   └── controller_test.go # for unit testing
│   ├── entities
│   │   ├── README.md
│   │   ├── test_data # test data folder
│   │   ├── user.go
│   │   └── post.go
│   ├── main.go
│   ├── assets # original static files, includes html. Program may change these files in build/assets.
│   │   ├── css # css files
│   │   ├── js # js files
│   │   ├── images # images
│   │   └── favicon.ico
│   ├── test
│   ├── go.mod
│   ├── go.sum
│   └── Makefile
├── .gitignore
├── README.md # entry point with overview and links to other docs
```

## 本地运行项目

### config /etc/hosts for server domain

```bash
#/etc/hosts
127.0.0.1    site1.local1 # should be match rent_report/src/web/vite.config.js target config
```

### 前端

```bash
cd src/web
npm install # if needed
npm run dev
```

### 后端

```bash
cd src
go mod download # if needed
go run main.go --config local.ini
```

## 部署

### 前端 
1. 进入src/web目录, 
2. 方法1: 运行 npm run build, 然后移动dist文件夹到所需位置, 修改nginx的配置文件, 将静态文件指向 dist/index.html
3. 方法2: 不更改nginx配置, 使用目前appweb配置:
将vite.config.js 的 BASE_URL 和 main.js 的 baseUrl 修改为 ’/web’, 然后运行 npm run build, 将dist目录下的所有文件放到原来appweb静态网页的位置, 即 cp -r dist/* /opt/appd9/appweb/src/webroot/public/web/

### 后端
1. 进入src目录
2. 修改local.ini 

```bash
[server]
port = "8089"
baseUrl = "/web"  ## 不更改nginx配置, 使用目前appweb配置
```

3. 编译&运行

```bash
go build ## see 4. test server go version match if needed
./rent_report --config local.ini
```

假如遇到问题gcc: error: unrecognized command-line option '-m64'
```export CGO_ENABLED=0```

4. test server go version match

目前测试服的go version 版本低, 可以下载依赖然后更改版本

```bash
cd rent_report
mkdir private-modules
cd private-modules
<NAME_EMAIL>:real-rm/XXX # for go-toml goconfig golog gomail gomongo
cd XXX # for go-toml goconfig golog gomail gomongo
vi go.mod # 每个依赖修改为测试服go version,  go 1.22.9
```

```bash
cd rent_report/src
vim go.mod # 添加下面的replace 使用下载的依赖
```

```
replace (
    github.com/real-rm/goconfig => ../private-modules/goconfig
    github.com/real-rm/golog => ../private-modules/golog
    github.com/real-rm/gomongo => ../private-modules/gomongo
    github.com/real-rm/gomail => ../private-modules/gomail
    github.com/real-rm/go-toml => ../private-modules/go-toml
)
```
