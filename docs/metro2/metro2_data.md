# Metro2 introduction

format doc: see ./Example of fixed length 426.docx

runalbe generation code: see ./metro2_gen

```bash
go run main.go
```

  - example input data: see ./metro2_gen/metro2_example_input.json
  - example output file: see ./metro2_gen/metro2_example.txt

## Format 

Note: all Example Value below is used for ./metro2_gen/metro2_example_input.json

## Header Segment

The Header Segment contains information about the data furnisher (the reporting entity).

| Field | Description | Example Value | Calculation (if applicable) |
|-------|-------------|---------------|----------------------------|
| recordDescriptorWord | Technical field for record length | 426, 526, 626 ... |  Length of the header record in bytes, 426 + 100 * maximum_J1_number_of_all_reporting_leases, should be fixed for the whole file |
| recordIdentifier | Identifies record type | "HEADER" | Fixed value |
| EquifaxProgramIdentifier | Identifies for Equifax | "1101653" | Fixed value |
| activityDate | Date of the reporting activity | "2025-03-31T00:00:00Z" | Should be month end date,Ex- for Feb, it should be 02/28/2025, CANNOT be future dated. For example, if you are reporting data for the month of March in April, the activity date should be Mar 31 2025. |
| dateCreated | Date the reporter was created | "2023-01-10T00:00:00Z" | This is the date when the file was created |
| programDate | Date of the program | "2023-01-10T00:00:00Z" | Software creation date |
| programRevisionDate | Date of last program revision | "2023-01-10T00:00:00Z" | Software update date |
| reporterName | Name of the reporting entity | "ONTARIO LANDLORD REPORTING SERVICES" | N/A |
| reporterAddress | Address of the reporting entity | "123 YONGE STREET SUITE 400 TORONTO ON M5C 2W7" | N/A |
| reporterTelephoneNumber | Phone number of the reporting entity | ********** | N/A |

## Base Segment

The Base Segment contains information about the primary consumer (tenant) and the account (rental agreement).

| Field | Description | Example Value | Calculation (if applicable) |
|-------|-------------|---------------|----------------------------|
| recordDescriptorWord | Technical field for record length | 426, 526, 626 ... | Length of the base record in bytes , 426 + 100 * maximum_J1_number_of_all_reporting_leases, should be fixed for the whole file |
| processingIndicator | Indicates how to process the record | 1 | Fixed value for standard processing |
| timeStamp | Time of record creation | "2023-04-01T10:15:22Z" | Current system time when record created |
| identificationNumber | Reporter's ID | "467RE01193" | Fixed value, Assigned by Equifax |
| consumerAccountNumber | Account number assigned to consumer | "*********" | uniq_tentant_id |
| portfolioType | Type of portfolio | "O" | Open Account (rent) |
| accountType | Type of account | "29" | Fixed code for rental agreements |
| dateOpened | Date the account was opened | "2021-06-15T00:00:00Z" | Lease start date |
| highestCredit | Total value of the lease | 1500 | When reporting high credit, this field should be reported with the monthly rental amount. |
| termsDuration | Duration of the lease | "012" | Number of months in lease agreement |
| scheduledMonthlyPaymentAmount |  Scheduled Monthly PaymentAmount  | 1500  | Scheduled  Monthly rent amount |
| actualPaymentAmount | actual payment amount | 1500 | actual payment amount |
| accountStatus | Current status of the account | "11", mulitple values see details below  | Code based on payment status  |
| paymentHistoryProfile | 24-month payment history | "BBBBBBBBBBBBBBBBBBBBBBBB" | 24 characters, one per month: previous 1, 2 ... to 24 months payment history, see below |
| specialComment | Special comment code | "" | N/A |
| currentBalance | Current balance on account | 0 | Typically $0 for rental accounts unless in arrears. When reporting delinquent accounts, the past due balance must be reported. |
| dateAccountInformation | Date of account information | "2025-03-31T00:00:00Z" | It can be the same as activity date |
| amountPastDue | Amount Past Due | 1500 | When reporting delinquent accounts, the amount past due must be reported, otherwise can be omitted |
| dateFirstDelinquency | Date of first delinquency |  "2023-02-15T00:00:00Z"  | When reporting delinquent accounts, the date of first delinquency must be reported, otherwise can be omitted|
| dateClosed | Date the account was closed | "2023-03-30T00:00:00Z" | Lease end dat, The date closed should be used when the contract is over and the tenant no longer rents the unit. |
| dateLastPayment | Date of last payment | "2023-03-15T00:00:00Z" | Date of most recent rent payment |
| surname | Consumer's last name | "JOHNSON" | N/A |
| firstName | Consumer's first name | "EMILY" | N/A |
| generationCode | Generation identifier | "" | N/A |
| socialSecurityNumber | Social Insurance Number | *********, Not mandatory | N/A |
| dateBirth | Date of birth | "1988-07-18T00:00:00Z" | N/A |
| telephoneNumber | Phone number | ********** | N/A |
| ecoaCode | Equal Credit Opportunity Act code | "1" | Code based on account responsibility, To be used for individual accounts and if accounts are joint, use 2 |
| countryCode | Country code | "CA" | ISO country code |
| firstLineAddress | First line of address | "35 Balmuto Street" | N/A |
| secondLineAddress | Second line of address | "Apt 1502" | N/A |
| city | City | "Toronto" | N/A |
| state | Province | "ON" | Provincial/state code |
| zipCode | Postal code | "M4Y 1W9" | N/A |
| addressIndicator | Address type indicator | "Y" | Code for confirmed address |

## J1 Segment (Associated Consumer)

The J1 Segment contains information about an additional consumer (co-tenant) on the account.

| Field | Description | Example Value | Calculation (if applicable) |
|-------|-------------|---------------|----------------------------|
| segmentIdentifier | Identifies segment type | "J1" | Fixed value |
| surname | Co-consumer's last name | "JOHNSON" | N/A |
| firstName | Co-consumer's first name | "MICHAEL" | N/A |
| generationCode | Generation identifier | "" | N/A |
| socialSecurityNumber | Social Insurance Number | ********* | N/A |
| dateBirth | Date of birth | "1986-04-12T00:00:00Z" | N/A |
| telephoneNumber | Phone number | ********** | N/A |
| ecoaCode | Equal Credit Opportunity Act code | "2" | Code for joint account responsibility, base segment should be reported with ECOA 2 as well |

## Important Metro 2 Codes

### Account Type Codes
- 29 = Rental Agreement

### Account Status Codes
- 11 = Current account, to be used if the account is not past due and for current accounts
- 13 = Paid or closed account/zero balance
- 71 = Account 30-59 days past due
- 78 = Account 60-89 days past due
- 80 = Account 90-119 days past due
- 82 = Account 120-149 days past due
- 83 = Account 150-179 days past due
- 84 = Account 180+ days past due

### ECOA Codes
- 1 = Individual/Main
- 2 = Joint account
- 3 = Authorized user
- 7 = Cosigner/Guarantor

### Payment History Profile
- 0 = Current (paid as agreed)
- 1 = 30-59 days past due
- 2 = 60-89 days past due
- 3 = 90-119 days past due
- 4 = 120-149 days past due
- 5 = 150-179 days past due
- 6 = 180 or more days past due date
- B = No payment history available prior to this time – either because the account was not open or because the payment history cannot be furnished. A "B" may not be embedded within other values.
- D = No payment history reported/available this month. "D" may be embedded in the payment history profile.
- E = Zero balance and current account (Applies to Credit Cards and Lines of Credit)
- G = Collection
- H = Foreclosure Completed
- J = Voluntary Surrender
- K = Repossession
- L = Charge-off

### Creditor Classification Codes
- 9 = Rental/Leasing

### Other useful links

https://help.loanpro.io/en_US/credit-reporting-options/how-metro2-files-work
