2025-08-06T11:41:53.446-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T11:58:59.031-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T12:15:55.702-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T12:54:17.353-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-08-06T12:57:30.4-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:05:16.354-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:06:02.041-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:06:02.301-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:06:07.545-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:07:01.422-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:08:01.547-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:09:01.658-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:09:01.658-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:10:01.765-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:11:01.876-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:12:02.004-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:12:02.005-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T13:13:51.528-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T16:19:03.35-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T16:19:50.418-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
2025-08-06T16:29:01.625-04:00 level=ERROR msg=failed to create collection, error={}, file="mongodb.go:184"
