2025-08-06T11:41:53.43-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T11:41:53.439-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T11:41:53.446-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T11:41:53.446-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T11:41:53.446-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T11:41:53.447-04:00 level=INFO msg=Created upload directory, path="../uploads/lease_documents"
2025-08-06T11:41:53.447-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/lease_docs"
2025-08-06T11:41:53.447-04:00 level=INFO msg=Created upload directory, path="../uploads/property_documents"
2025-08-06T11:41:53.447-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/property_docs"
2025-08-06T11:41:53.447-04:00 level=INFO msg=Created upload directory, path="../uploads/problem_reports"
2025-08-06T11:41:53.447-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/problem_files"
2025-08-06T11:41:53.447-04:00 level=INFO msg=Created upload directory, path="../uploads/metro2_reports"
2025-08-06T11:41:53.447-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/metro2_files"
2025-08-06T11:41:53.447-04:00 level=INFO, value="Goupload configuration validated successfully"
2025-08-06T11:41:53.447-04:00 level=INFO, value="Goupload initialized successfully"
2025-08-06T11:41:53.448-04:00 level=INFO msg=Static file serving enabled, path="../src/web/dist"
2025-08-06T11:41:53.448-04:00 level=INFO msg=Server starting, port=8089
2025-08-06T11:41:53.449-04:00 level=INFO msg=Server available, url="http://localhost:8089/"
2025-08-06T11:41:59.235-04:00 level=INFO msg=Sensitive operation started, path="/v1/auth/logout", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:41:59-04:00", method="POST"
2025-08-06T11:41:59.236-04:00 level=INFO msg=Sensitive operation completed, status=204, duration="1.487963ms", clientIP="::1", responseSize=0, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="POST", path="/v1/auth/logout"
2025-08-06T11:42:00.448-04:00 level=INFO msg=Sensitive operation started, path="/v1/auth/check", clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:42:00-04:00", method="GET"
2025-08-06T11:42:00.84-04:00 level=INFO msg=Sensitive operation started, userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:42:00-04:00", method="GET", path="/v1/auth/check", clientIP="::1"
2025-08-06T11:42:01.264-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:42:01-04:00", method="GET", path="/v1/auth/check", clientIP="::1", userID=""
2025-08-06T11:42:08.155-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/login", clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:42:08-04:00"
2025-08-06T11:42:08.258-04:00 level=INFO msg=Sensitive operation completed, responseSize=163, method="POST", path="/v1/auth/login", status=200, duration="103.341087ms", clientIP="::1"
2025-08-06T11:58:59.016-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T11:58:59.024-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T11:58:59.031-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T11:58:59.031-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T11:58:59.031-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T11:58:59.031-04:00 level=INFO msg=Created upload directory, path="../uploads/problem_reports"
2025-08-06T11:58:59.031-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/problem_files"
2025-08-06T11:58:59.031-04:00 level=INFO msg=Created upload directory, path="../uploads/metro2_reports"
2025-08-06T11:58:59.031-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/metro2_files"
2025-08-06T11:58:59.031-04:00 level=INFO msg=Created upload directory, path="../uploads/lease_documents"
2025-08-06T11:58:59.031-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/lease_docs"
2025-08-06T11:58:59.031-04:00 level=INFO msg=Created upload directory, path="../uploads/property_documents"
2025-08-06T11:58:59.031-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/property_docs"
2025-08-06T11:58:59.031-04:00 level=INFO, value="Goupload configuration validated successfully"
2025-08-06T11:58:59.031-04:00 level=INFO, value="Goupload initialized successfully"
2025-08-06T11:58:59.033-04:00 level=INFO msg=Static file serving enabled, path="../src/web/dist"
2025-08-06T11:58:59.033-04:00 level=INFO msg=Server starting, port=8089
2025-08-06T11:58:59.033-04:00 level=INFO msg=Server available, url="http://localhost:8089/"
2025-08-06T11:59:18.251-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST"
2025-08-06T11:59:18.254-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-06T11:59:18.255-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "../uploads/lease_documents"
]
2025-08-06T11:59:18.256-04:00 level=INFO, value="Using L2_FOLDER_LIST from constants package"
2025-08-06T11:59:18.258-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": true,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-06T11:59:53.938-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/logout", clientIP="::1", userID="ZbqaKSetPJE", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:59:53-04:00"
2025-08-06T11:59:53.939-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=0, userID="ZbqaKSetPJE", method="POST", path="/v1/auth/logout", status=204, duration="1.973256ms"
2025-08-06T11:59:57.397-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:59:57-04:00", method="POST", path="/v1/auth/login", clientIP="::1", userID=""
2025-08-06T11:59:57.496-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=178, method="POST", path="/v1/auth/login", status=200, duration="99.371935ms"
2025-08-06T11:59:57.541-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:59:57-04:00"
2025-08-06T11:59:57.544-04:00 level=INFO msg=Sensitive operation completed, status=200, duration="3.331385ms", clientIP="::1", responseSize=1313, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users"
2025-08-06T11:59:57.544-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T11:59:59.879-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/pages/admin/user_detail/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T11:59:59.896-04:00 level=INFO msg=Admin operation, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET", path="/pages/admin/user_detail/main.js"
2025-08-06T11:59:59.912-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:59:59-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE"
2025-08-06T11:59:59.914-04:00 level=INFO msg=Sensitive operation completed, status=200, duration="2.840675ms", clientIP="::1", responseSize=147, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE"
2025-08-06T11:59:59.915-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T11:59:59.917-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:59:59-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties"
2025-08-06T11:59:59.924-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=324, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", status=200, duration="7.001647ms"
2025-08-06T11:59:59.924-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T11:59:59.927-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:59:59-04:00"
2025-08-06T11:59:59.943-04:00 level=INFO msg=Sensitive operation completed, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", status=200, duration="16.716927ms", clientIP="::1", responseSize=1493
2025-08-06T11:59:59.943-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T11:59:59.946-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/ZbqaKSetPJE/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:59:59-04:00", method="GET"
2025-08-06T11:59:59.953-04:00 level=INFO msg=Sensitive operation completed, responseSize=324, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", status=200, duration="7.466107ms", clientIP="::1"
2025-08-06T11:59:59.953-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T11:59:59.955-04:00 level=INFO msg=Sensitive operation started, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T11:59:59-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1"
2025-08-06T11:59:59.965-04:00 level=INFO msg=Sensitive operation completed, path="/v1/admin/users/ZbqaKSetPJE/leases", status=200, duration="10.457782ms", clientIP="::1", responseSize=1493, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET"
2025-08-06T11:59:59.965-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:03.187-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/pages/admin/lease_all/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T12:00:03.205-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/lease_all/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:03.212-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:03-04:00"
2025-08-06T12:00:03.214-04:00 level=INFO msg=Sensitive operation completed, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE", status=200, duration="2.397632ms", clientIP="::1", responseSize=147
2025-08-06T12:00:03.215-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:03.221-04:00 level=INFO msg=Sensitive operation started, timestamp="2025-08-06T12:00:03-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-06T12:00:03.234-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", status=200, duration="13.824418ms", clientIP="::1", responseSize=1494, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:00:03.235-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:04.83-04:00 level=INFO msg=Admin operation, path="/pages/admin/lease_detail/index.html", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=301, method="GET"
2025-08-06T12:00:04.833-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/lease_detail/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:04.853-04:00 level=INFO msg=Admin operation, clientIP="::1", status=200, method="GET", path="/pages/admin/lease_detail/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:00:04.861-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/leases/UENxtYxX6UE", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:04-04:00"
2025-08-06T12:00:04.87-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/leases/UENxtYxX6UE", status=200, duration="8.576819ms", clientIP="::1", responseSize=952, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:00:04.87-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/leases/UENxtYxX6UE", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:04.886-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/leases/UENxtYxX6UE/payments", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:04-04:00"
2025-08-06T12:00:04.89-04:00 level=INFO msg=Sensitive operation completed, path="/v1/admin/leases/UENxtYxX6UE/payments", status=200, duration="4.832266ms", clientIP="::1", responseSize=170, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET"
2025-08-06T12:00:04.89-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/leases/UENxtYxX6UE/payments", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:08.743-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:08-04:00"
2025-08-06T12:00:08.746-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/ZbqaKSetPJE", status=200, duration="2.365674ms", clientIP="::1", responseSize=147, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:00:08.746-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:08.749-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:08-04:00"
2025-08-06T12:00:08.762-04:00 level=INFO msg=Sensitive operation completed, duration="12.823123ms", clientIP="::1", responseSize=1494, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", status=200
2025-08-06T12:00:08.762-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:09.231-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/user_detail/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=304
2025-08-06T12:00:09.248-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/user_detail/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=304
2025-08-06T12:00:09.256-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/ZbqaKSetPJE", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:09-04:00", method="GET"
2025-08-06T12:00:09.259-04:00 level=INFO msg=Sensitive operation completed, responseSize=147, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE", status=200, duration="2.842759ms", clientIP="::1"
2025-08-06T12:00:09.259-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:09.268-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:09-04:00"
2025-08-06T12:00:09.277-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=324, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", status=200, duration="8.844486ms"
2025-08-06T12:00:09.277-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:09.28-04:00 level=INFO msg=Sensitive operation started, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:09-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1"
2025-08-06T12:00:09.293-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=1493, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", status=200, duration="13.633209ms"
2025-08-06T12:00:09.293-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:09.295-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:09-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties"
2025-08-06T12:00:09.303-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", status=200, duration="8.131442ms", clientIP="::1", responseSize=324, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:00:09.303-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:09.306-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:09-04:00"
2025-08-06T12:00:09.326-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", status=200, duration="19.844062ms", clientIP="::1", responseSize=1493, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:00:09.326-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T12:00:09.758-04:00 level=INFO msg=Sensitive operation started, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:09-04:00", method="GET", path="/v1/admin/users", clientIP="::1"
2025-08-06T12:00:09.763-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=1313, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users", status=200, duration="5.103474ms"
2025-08-06T12:00:09.763-04:00 level=INFO msg=Admin operation, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET", path="/v1/admin/users"
2025-08-06T12:00:10.314-04:00 level=INFO msg=Sensitive operation started, timestamp="2025-08-06T12:00:10-04:00", method="GET", path="/v1/admin/users", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-06T12:00:10.319-04:00 level=INFO msg=Sensitive operation completed, duration="4.656598ms", clientIP="::1", responseSize=1313, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users", status=200
2025-08-06T12:00:10.319-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:00:12.306-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:12-04:00", method="POST", path="/v1/auth/logout", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:00:12.306-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=0, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="POST", path="/v1/auth/logout", status=204, duration="792.586µs"
2025-08-06T12:00:16.884-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/login", clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:00:16-04:00"
2025-08-06T12:00:16.98-04:00 level=INFO msg=Sensitive operation completed, path="/v1/auth/login", status=200, duration="96.147045ms", clientIP="::1", responseSize=163, method="POST"
2025-08-06T12:02:25.328-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/logout", clientIP="::1", userID="blZ85CFN2CF", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:02:25-04:00"
2025-08-06T12:02:25.329-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=0, userID="blZ85CFN2CF", method="POST", path="/v1/auth/logout", status=204, duration="719.59µs"
2025-08-06T12:15:55.686-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T12:15:55.695-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T12:15:55.701-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T12:15:55.701-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T12:15:55.702-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T12:15:55.702-04:00 level=INFO msg=Created upload directory, path="../uploads/lease_documents"
2025-08-06T12:15:55.702-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/lease_docs"
2025-08-06T12:15:55.702-04:00 level=INFO msg=Created upload directory, path="../uploads/property_documents"
2025-08-06T12:15:55.702-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/property_docs"
2025-08-06T12:15:55.702-04:00 level=INFO msg=Created upload directory, path="../uploads/problem_reports"
2025-08-06T12:15:55.702-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/problem_files"
2025-08-06T12:15:55.702-04:00 level=INFO msg=Created upload directory, path="../uploads/metro2_reports"
2025-08-06T12:15:55.702-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/metro2_files"
2025-08-06T12:15:55.702-04:00 level=INFO, value="Goupload configuration validated successfully"
2025-08-06T12:15:55.702-04:00 level=INFO, value="Goupload initialized successfully"
2025-08-06T12:15:55.703-04:00 level=INFO msg=Static file serving enabled, path="../src/web/dist"
2025-08-06T12:15:55.703-04:00 level=INFO msg=Server starting, port=8089
2025-08-06T12:15:55.704-04:00 level=INFO msg=Server available, url="http://localhost:8089/"
2025-08-06T12:16:03.192-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:03-04:00", method="DELETE", path="/v1/leases/UENxtYxX6UE/document/WZ6ROl4QXzB", clientIP="::1", userID="ZbqaKSetPJE"
2025-08-06T12:16:03.223-04:00 level=INFO msg=Sensitive operation completed, method="DELETE", path="/v1/leases/UENxtYxX6UE/document/WZ6ROl4QXzB", status=200, duration="31.383375ms", clientIP="::1", responseSize=414, userID="ZbqaKSetPJE"
2025-08-06T12:16:05.419-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:05-04:00", method="GET", path="/v1/leases/UENxtYxX6UE/documents/deleted", clientIP="::1", userID="ZbqaKSetPJE"
2025-08-06T12:16:05.431-04:00 level=INFO msg=Sensitive operation completed, responseSize=284, userID="ZbqaKSetPJE", method="GET", path="/v1/leases/UENxtYxX6UE/documents/deleted", status=200, duration="12.04065ms", clientIP="::1"
2025-08-06T12:16:10.05-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST"
2025-08-06T12:16:10.053-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-06T12:16:10.054-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "../uploads/lease_documents"
]
2025-08-06T12:16:10.055-04:00 level=INFO, value="Using L2_FOLDER_LIST from constants package"
2025-08-06T12:16:10.057-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": true,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-06T12:16:33.304-04:00 level=INFO msg=Sensitive operation started, userID="ZbqaKSetPJE", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:33-04:00", method="POST", path="/v1/auth/logout", clientIP="::1"
2025-08-06T12:16:33.306-04:00 level=INFO msg=Sensitive operation completed, method="POST", path="/v1/auth/logout", status=204, duration="2.253747ms", clientIP="::1", responseSize=0, userID="ZbqaKSetPJE"
2025-08-06T12:16:37.079-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/login", clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:37-04:00"
2025-08-06T12:16:37.178-04:00 level=INFO msg=Sensitive operation completed, duration="99.235703ms", clientIP="::1", responseSize=178, method="POST", path="/v1/auth/login", status=200
2025-08-06T12:16:37.212-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:37-04:00"
2025-08-06T12:16:37.215-04:00 level=INFO msg=Sensitive operation completed, duration="3.965078ms", clientIP="::1", responseSize=1315, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users", status=200
2025-08-06T12:16:37.215-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:41.918-04:00 level=INFO msg=Admin operation, path="/pages/admin/user_detail/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET"
2025-08-06T12:16:41.933-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/user_detail/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:41.943-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:41-04:00"
2025-08-06T12:16:41.947-04:00 level=INFO msg=Sensitive operation completed, duration="3.823954ms", clientIP="::1", responseSize=149, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE", status=200
2025-08-06T12:16:41.947-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:41.949-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:41-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties"
2025-08-06T12:16:41.957-04:00 level=INFO msg=Sensitive operation completed, path="/v1/admin/users/ZbqaKSetPJE/properties", status=200, duration="7.988448ms", clientIP="::1", responseSize=324, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET"
2025-08-06T12:16:41.957-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:41.96-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:41-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases"
2025-08-06T12:16:41.974-04:00 level=INFO msg=Sensitive operation completed, duration="14.377898ms", clientIP="::1", responseSize=1493, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", status=200
2025-08-06T12:16:41.974-04:00 level=INFO msg=Admin operation, clientIP="::1", status=200, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:16:41.977-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:41-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties"
2025-08-06T12:16:41.985-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", status=200, duration="7.230657ms", clientIP="::1", responseSize=324, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:16:41.985-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T12:16:41.987-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:41-04:00", method="GET"
2025-08-06T12:16:42.006-04:00 level=INFO msg=Sensitive operation completed, path="/v1/admin/users/ZbqaKSetPJE/leases", status=200, duration="19.074892ms", clientIP="::1", responseSize=1493, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET"
2025-08-06T12:16:42.006-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:43.171-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/pages/admin/lease_all/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T12:16:43.182-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/lease_all/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:43.191-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:43-04:00"
2025-08-06T12:16:43.193-04:00 level=INFO msg=Sensitive operation completed, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE", status=200, duration="2.496247ms", clientIP="::1", responseSize=149
2025-08-06T12:16:43.193-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:43.197-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:43-04:00", method="GET"
2025-08-06T12:16:43.208-04:00 level=INFO msg=Sensitive operation completed, status=200, duration="11.44911ms", clientIP="::1", responseSize=1494, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases"
2025-08-06T12:16:43.208-04:00 level=INFO msg=Admin operation, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases"
2025-08-06T12:16:44.666-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/lease_detail/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:44.681-04:00 level=INFO msg=Admin operation, clientIP="::1", status=200, method="GET", path="/pages/admin/lease_detail/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:16:44.694-04:00 level=INFO msg=Sensitive operation started, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:44-04:00", method="GET", path="/v1/admin/leases/UENxtYxX6UE", clientIP="::1"
2025-08-06T12:16:44.7-04:00 level=INFO msg=Sensitive operation completed, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/leases/UENxtYxX6UE", status=200, duration="6.371242ms", clientIP="::1", responseSize=1297
2025-08-06T12:16:44.7-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/leases/UENxtYxX6UE", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:44.709-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:44-04:00", method="GET", path="/v1/admin/leases/UENxtYxX6UE/payments", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:16:44.714-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/leases/UENxtYxX6UE/payments", status=200, duration="5.9152ms", clientIP="::1", responseSize=170, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:16:44.714-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/leases/UENxtYxX6UE/payments", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:53.051-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/lease_all/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=304
2025-08-06T12:16:53.06-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/lease_all/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=304
2025-08-06T12:16:53.067-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:53-04:00"
2025-08-06T12:16:53.07-04:00 level=INFO msg=Sensitive operation completed, responseSize=149, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE", status=200, duration="2.595621ms", clientIP="::1"
2025-08-06T12:16:53.07-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:53.072-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:53-04:00"
2025-08-06T12:16:53.082-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=1494, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", status=200, duration="9.984362ms"
2025-08-06T12:16:53.082-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T12:16:53.96-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/user_detail/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=304
2025-08-06T12:16:53.971-04:00 level=INFO msg=Admin operation, clientIP="::1", status=304, method="GET", path="/pages/admin/user_detail/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:16:53.983-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:53-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:16:53.988-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/ZbqaKSetPJE", status=200, duration="5.261201ms", clientIP="::1", responseSize=149, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:16:53.988-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users/ZbqaKSetPJE", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T12:16:53.991-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:53-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:16:53.999-04:00 level=INFO msg=Sensitive operation completed, responseSize=324, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", status=200, duration="8.261906ms", clientIP="::1"
2025-08-06T12:16:53.999-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:54.002-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:54-04:00"
2025-08-06T12:16:54.015-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=1493, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", status=200, duration="12.77415ms"
2025-08-06T12:16:54.015-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T12:16:54.018-04:00 level=INFO msg=Sensitive operation started, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:54-04:00", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", clientIP="::1"
2025-08-06T12:16:54.026-04:00 level=INFO msg=Sensitive operation completed, duration="8.306822ms", clientIP="::1", responseSize=324, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", status=200
2025-08-06T12:16:54.026-04:00 level=INFO msg=Admin operation, clientIP="::1", status=200, method="GET", path="/v1/admin/users/ZbqaKSetPJE/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T12:16:54.028-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/ZbqaKSetPJE/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:54-04:00", method="GET"
2025-08-06T12:16:54.043-04:00 level=INFO msg=Sensitive operation completed, duration="14.559814ms", clientIP="::1", responseSize=1493, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", status=200
2025-08-06T12:16:54.043-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/ZbqaKSetPJE/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:55.297-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:55-04:00"
2025-08-06T12:16:55.301-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=1315, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users", status=200, duration="3.983453ms"
2025-08-06T12:16:55.301-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T12:16:58.207-04:00 level=INFO msg=Sensitive operation started, path="/v1/auth/logout", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:16:58-04:00", method="POST"
2025-08-06T12:16:58.207-04:00 level=INFO msg=Sensitive operation completed, duration="610.624µs", clientIP="::1", responseSize=0, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="POST", path="/v1/auth/logout", status=204
2025-08-06T12:31:59.238-04:00 level=INFO msg=Sensitive operation started, timestamp="2025-08-06T12:31:59-04:00", method="POST", path="/v1/auth/login", clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-06T12:31:59.337-04:00 level=INFO msg=Sensitive operation completed, method="POST", path="/v1/auth/login", status=200, duration="99.396403ms", clientIP="::1", responseSize=170
2025-08-06T12:32:32.006-04:00 level=INFO msg=Sensitive operation started, userID="blZ85CFN2CF", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T12:32:32-04:00", method="DELETE", path="/v1/subscriptions/sub_1RsR3QRdRW2qyPyrCF6RVjrW", clientIP="::1"
2025-08-06T12:32:32.73-04:00 level=INFO msg=Initiated rent reporting paused notification emails (user subscription cancelled), leaseId="qlNNg3B7ymy", tenantCount=1, landlordName="Vicky Zheng", propertyAddress="3, a, ON X0X 0X0, Canada", propertyId="xibQfBmee9X"
2025-08-06T12:32:32.73-04:00 level=INFO msg=Sensitive operation completed, userID="blZ85CFN2CF", method="DELETE", path="/v1/subscriptions/sub_1RsR3QRdRW2qyPyrCF6RVjrW", status=200, duration="724.607219ms", clientIP="::1", responseSize=16
2025-08-06T12:32:32.742-04:00 level=INFO msg=Use mail engine, engine="gmail", from="RealMaster \<EMAIL>\u003e"
2025-08-06T12:32:34.228-04:00 level=INFO msg=Successfully sent rent reporting paused notification email, email="<EMAIL>", firstName="Vicky"
2025-08-06T12:54:17.339-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T12:54:17.344-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T12:54:17.35-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T12:54:17.351-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T12:54:17.353-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T12:54:17.354-04:00 level=INFO msg=Processing Metro2 notification tasks, cleanupDays=30, delayHours=0, delayMinutes=1, processIntervalMinutes=1
2025-08-06T12:54:17.355-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T12:54:17.36-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T12:54:17.361-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T12:54:17.365-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T12:54:17.366-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-06T12:57:30.389-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T12:57:30.394-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T12:57:30.399-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T12:57:30.399-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T12:57:30.401-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T12:57:30.402-04:00 level=INFO msg=Processing Metro2 notification tasks, delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-08-06T12:57:30.402-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T12:57:30.406-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T12:57:30.407-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T12:57:30.41-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T12:57:30.411-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-06T13:05:16.339-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:05:16.346-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:05:16.352-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:05:16.353-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:05:16.355-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T13:06:02.031-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:06:02.035-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:06:02.04-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:06:02.041-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:06:02.041-04:00 level=INFO, value="Starting rent report email batch CLI execution..."
2025-08-06T13:06:02.042-04:00 level=INFO msg=Processing rent report email batch, windowMinutes=120
2025-08-06T13:06:02.045-04:00 level=INFO, value="No unprocessed rent report email records found"
2025-08-06T13:06:02.045-04:00 level=INFO, value="Rent report email batch CLI completed successfully"
2025-08-06T13:06:02.294-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:06:02.297-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:06:02.3-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:06:02.301-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:06:02.301-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T13:06:02.301-04:00 level=INFO msg=Processing Metro2 notification tasks, delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-08-06T13:06:02.302-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T13:06:02.304-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T13:06:02.304-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T13:06:02.306-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T13:06:02.306-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-06T13:06:07.532-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:06:07.538-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:06:07.544-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:06:07.544-04:00 level=INFO msg=Registered predefined collection, collName="mail_log", collection="mailLog", database="rr"
2025-08-06T13:06:07.545-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T13:06:07.546-04:00 level=INFO msg=Processing Metro2 notification tasks, delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-08-06T13:06:07.547-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T13:06:07.551-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T13:06:07.551-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T13:06:07.555-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T13:06:07.555-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-06T13:07:01.414-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:07:01.418-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:07:01.421-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:07:01.421-04:00 level=INFO msg=Registered predefined collection, collName="mail_log", collection="mailLog", database="rr"
2025-08-06T13:07:01.422-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T13:07:01.422-04:00 level=INFO msg=Processing Metro2 notification tasks, delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-08-06T13:07:01.423-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T13:07:01.425-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T13:07:01.425-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T13:07:01.427-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T13:07:01.427-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-06T13:08:01.539-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:08:01.542-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:08:01.546-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:08:01.546-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:08:01.547-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T13:08:01.547-04:00 level=INFO msg=Processing Metro2 notification tasks, delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-08-06T13:08:01.548-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T13:08:01.549-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T13:08:01.55-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T13:08:01.552-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T13:08:01.552-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-06T13:09:01.65-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:09:01.65-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:09:01.653-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:09:01.654-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:09:01.657-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:09:01.657-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:09:01.657-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:09:01.658-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:09:01.658-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T13:09:01.659-04:00 level=INFO, value="Starting rent report email batch CLI execution..."
2025-08-06T13:09:01.658-04:00 level=INFO msg=Processing Metro2 notification tasks, cleanupDays=30, delayHours=0, delayMinutes=1, processIntervalMinutes=1
2025-08-06T13:09:01.659-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T13:09:01.659-04:00 level=INFO msg=Processing rent report email batch, windowMinutes=120
2025-08-06T13:09:01.661-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T13:09:01.661-04:00 level=INFO, value="No unprocessed rent report email records found"
2025-08-06T13:09:01.662-04:00 level=INFO, value="Rent report email batch CLI completed successfully"
2025-08-06T13:09:01.662-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T13:09:01.664-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T13:09:01.664-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-06T13:10:01.755-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:10:01.759-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:10:01.764-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:10:01.764-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:10:01.765-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T13:10:01.765-04:00 level=INFO msg=Processing Metro2 notification tasks, delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-08-06T13:10:01.766-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T13:10:01.768-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T13:10:01.769-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T13:10:01.771-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T13:10:01.772-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-06T13:11:01.867-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:11:01.871-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:11:01.875-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:11:01.875-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:11:01.876-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T13:11:01.877-04:00 level=INFO msg=Processing Metro2 notification tasks, delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-08-06T13:11:01.877-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T13:11:01.879-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T13:11:01.88-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T13:11:01.882-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T13:11:01.882-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-06T13:12:01.995-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:12:01.996-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:12:01.999-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:12:02-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:12:02.002-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:12:02.003-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:12:02.004-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:12:02.004-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:12:02.004-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T13:12:02.005-04:00 level=INFO msg=Processing Metro2 notification tasks, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30, delayHours=0
2025-08-06T13:12:02.005-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T13:12:02.005-04:00 level=INFO, value="Starting rent report email batch CLI execution..."
2025-08-06T13:12:02.006-04:00 level=INFO msg=Processing rent report email batch, windowMinutes=120
2025-08-06T13:12:02.008-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T13:12:02.008-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T13:12:02.008-04:00 level=INFO, value="No unprocessed rent report email records found"
2025-08-06T13:12:02.009-04:00 level=INFO, value="Rent report email batch CLI completed successfully"
2025-08-06T13:12:02.01-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T13:12:02.01-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
2025-08-06T13:13:51.514-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T13:13:51.521-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T13:13:51.528-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T13:13:51.528-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T13:13:51.528-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T13:13:51.528-04:00 level=INFO msg=Created upload directory, path="../uploads/property_documents"
2025-08-06T13:13:51.528-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/property_docs"
2025-08-06T13:13:51.528-04:00 level=INFO msg=Created upload directory, path="../uploads/problem_reports"
2025-08-06T13:13:51.528-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/problem_files"
2025-08-06T13:13:51.528-04:00 level=INFO msg=Created upload directory, path="../uploads/metro2_reports"
2025-08-06T13:13:51.528-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/metro2_files"
2025-08-06T13:13:51.528-04:00 level=INFO msg=Created upload directory, path="../uploads/lease_documents"
2025-08-06T13:13:51.528-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/lease_docs"
2025-08-06T13:13:51.529-04:00 level=INFO, value="Goupload configuration validated successfully"
2025-08-06T13:13:51.529-04:00 level=INFO, value="Goupload initialized successfully"
2025-08-06T13:13:51.53-04:00 level=INFO msg=Static file serving enabled, path="../src/web/dist"
2025-08-06T13:13:51.53-04:00 level=INFO msg=Server starting, port=8089
2025-08-06T13:13:51.53-04:00 level=INFO msg=Server available, url="http://localhost:8089/"
2025-08-06T13:13:58.827-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="ZbqaKSetPJE", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T13:13:58-04:00", method="DELETE", path="/v1/leases/UENxtYxX6UE/document/hxrH3i0oNsW"
2025-08-06T13:13:58.865-04:00 level=INFO msg=Sensitive operation completed, responseSize=414, userID="ZbqaKSetPJE", method="DELETE", path="/v1/leases/UENxtYxX6UE/document/hxrH3i0oNsW", status=200, duration="39.062339ms", clientIP="::1"
2025-08-06T13:14:03.694-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST"
2025-08-06T13:14:03.695-04:00 level=INFO msg=Loaded current L1, l1=100, board="TEST"
2025-08-06T13:14:03.696-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "../uploads/lease_documents"
]
2025-08-06T13:14:03.697-04:00 level=INFO, value="Using L2_FOLDER_LIST from constants package"
2025-08-06T13:14:03.698-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": true,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-06T14:20:04.051-04:00 level=INFO msg=Sensitive operation started, timestamp="2025-08-06T14:20:04-04:00", method="DELETE", path="/v1/leases/DqGdbc4izdD", clientIP="::1", userID="blZ85CFN2CF", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-06T14:20:04.072-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST"
2025-08-06T14:20:04.074-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-06T14:20:04.075-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "../uploads/lease_documents"
]
2025-08-06T14:20:04.075-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": true,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-06T14:20:04.079-04:00 level=INFO msg=saving L2 list, l1=1279, force=true, board="TEST"
2025-08-06T14:20:04.092-04:00 level=INFO msg=Successfully updated L2 list, board="TEST", l1=1279, array_size=128, force=true
2025-08-06T14:20:04.092-04:00 level=INFO msg=Successfully deleted lease file, leaseId="DqGdbc4izdD", filePath="1279/a5c61/isueb07xkxhkYVVmM8qlq.png"
2025-08-06T14:20:04.092-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": true,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-06T14:20:04.093-04:00 level=INFO msg=Successfully deleted lease file, leaseId="DqGdbc4izdD", filePath="1279/a828c/lLacKnpDZz-YQY2dgl7nF.pdf"
2025-08-06T14:20:04.1-04:00 level=INFO msg=Successfully deleted lease payments, leaseId="DqGdbc4izdD", deletedCount=0
2025-08-06T14:20:04.108-04:00 level=INFO msg=Successfully deleted lease and all related data, leaseId="DqGdbc4izdD", fileCount=2
2025-08-06T14:20:04.123-04:00 level=INFO msg=Sensitive operation completed, method="DELETE", path="/v1/leases/DqGdbc4izdD", status=204, duration="73.013954ms", clientIP="::1", responseSize=-1, userID="blZ85CFN2CF"
2025-08-06T14:22:02.964-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/logout", clientIP="::1", userID="blZ85CFN2CF", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:22:02-04:00"
2025-08-06T14:22:02.967-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=0, userID="blZ85CFN2CF", method="POST", path="/v1/auth/logout", status=204, duration="3.539875ms"
2025-08-06T14:22:08.416-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/login", clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:22:08-04:00"
2025-08-06T14:22:11.579-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:22:11-04:00", method="POST", path="/v1/auth/login", clientIP="::1", userID=""
2025-08-06T14:22:24.776-04:00 level=INFO msg=Sensitive operation started, timestamp="2025-08-06T14:22:24-04:00", method="POST", path="/v1/auth/login", clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-06T14:22:31.219-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/login", clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:22:31-04:00"
2025-08-06T14:27:11.696-04:00 level=INFO msg=Sensitive operation started, path="/v1/auth/logout", clientIP="::1", userID="ZbqaKSetPJE", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:11-04:00", method="POST"
2025-08-06T14:27:11.697-04:00 level=INFO msg=Sensitive operation completed, method="POST", path="/v1/auth/logout", status=204, duration="851.791µs", clientIP="::1", responseSize=0, userID="ZbqaKSetPJE"
2025-08-06T14:27:15.633-04:00 level=INFO msg=Sensitive operation started, userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:15-04:00", method="POST", path="/v1/auth/login", clientIP="::1"
2025-08-06T14:27:15.73-04:00 level=INFO msg=Sensitive operation completed, method="POST", path="/v1/auth/login", status=200, duration="97.668887ms", clientIP="::1", responseSize=178
2025-08-06T14:27:15.742-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:15.769-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:15-04:00", method="GET"
2025-08-06T14:27:15.774-04:00 level=INFO msg=Sensitive operation completed, responseSize=1312, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users", status=200, duration="4.900619ms", clientIP="::1"
2025-08-06T14:27:15.774-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T14:27:17.485-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/user_detail/index.html", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=301
2025-08-06T14:27:17.49-04:00 level=INFO msg=Admin operation, clientIP="::1", status=200, method="GET", path="/pages/admin/user_detail/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:27:17.509-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/user_detail/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:17.523-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/blZ85CFN2CF", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:17-04:00", method="GET"
2025-08-06T14:27:17.528-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF", status=200, duration="5.145619ms", clientIP="::1", responseSize=153, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:27:17.528-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:17.531-04:00 level=INFO msg=Sensitive operation started, timestamp="2025-08-06T14:27:17-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-06T14:27:17.548-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", status=200, duration="17.61423ms", clientIP="::1", responseSize=1262, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:27:17.548-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T14:27:17.55-04:00 level=INFO msg=Sensitive operation started, timestamp="2025-08-06T14:27:17-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-06T14:27:17.565-04:00 level=INFO msg=Sensitive operation completed, status=200, duration="14.976358ms", clientIP="::1", responseSize=3505, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases"
2025-08-06T14:27:17.565-04:00 level=INFO msg=Admin operation, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases"
2025-08-06T14:27:17.567-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:17-04:00"
2025-08-06T14:27:17.581-04:00 level=INFO msg=Sensitive operation completed, responseSize=1262, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", status=200, duration="13.429484ms", clientIP="::1"
2025-08-06T14:27:17.581-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:17.583-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/blZ85CFN2CF/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:17-04:00", method="GET"
2025-08-06T14:27:17.598-04:00 level=INFO msg=Sensitive operation completed, path="/v1/admin/users/blZ85CFN2CF/leases", status=200, duration="14.860691ms", clientIP="::1", responseSize=3505, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET"
2025-08-06T14:27:17.598-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:27.837-04:00 level=INFO msg=Admin operation, status=301, method="GET", path="/pages/admin/property_all/index.html", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T14:27:27.84-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/property_all/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:27.856-04:00 level=INFO msg=Admin operation, path="/pages/admin/property_all/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET"
2025-08-06T14:27:27.864-04:00 level=INFO msg=Sensitive operation started, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:27-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF", clientIP="::1"
2025-08-06T14:27:27.867-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=153, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF", status=200, duration="2.727414ms"
2025-08-06T14:27:27.867-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:27.871-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:27-04:00", method="GET"
2025-08-06T14:27:27.905-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", status=200, duration="34.307918ms", clientIP="::1", responseSize=4287, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:27:27.905-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:36.537-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/blZ85CFN2CF", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:36-04:00"
2025-08-06T14:27:36.54-04:00 level=INFO msg=Sensitive operation completed, duration="3.282412ms", clientIP="::1", responseSize=153, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF", status=200
2025-08-06T14:27:36.54-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:36.544-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:36-04:00", method="GET"
2025-08-06T14:27:36.555-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", status=200, duration="11.362278ms", clientIP="::1", responseSize=1262, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:27:36.555-04:00 level=INFO msg=Admin operation, path="/v1/admin/users/blZ85CFN2CF/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET"
2025-08-06T14:27:36.558-04:00 level=INFO msg=Sensitive operation started, timestamp="2025-08-06T14:27:36-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-06T14:27:36.571-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", status=200, duration="13.458443ms", clientIP="::1", responseSize=3505, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:27:36.571-04:00 level=INFO msg=Admin operation, path="/v1/admin/users/blZ85CFN2CF/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET"
2025-08-06T14:27:36.574-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:36-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:27:36.587-04:00 level=INFO msg=Sensitive operation completed, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", status=200, duration="13.200193ms", clientIP="::1", responseSize=1262
2025-08-06T14:27:36.587-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:36.589-04:00 level=INFO msg=Sensitive operation started, timestamp="2025-08-06T14:27:36-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-06T14:27:36.601-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", status=200, duration="12.242236ms", clientIP="::1", responseSize=3505, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:27:36.601-04:00 level=INFO msg=Admin operation, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases"
2025-08-06T14:27:39.717-04:00 level=INFO msg=Sensitive operation started, timestamp="2025-08-06T14:27:39-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-08-06T14:27:39.719-04:00 level=INFO msg=Sensitive operation completed, path="/v1/admin/users/blZ85CFN2CF", status=200, duration="2.358331ms", clientIP="::1", responseSize=153, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET"
2025-08-06T14:27:39.719-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:39.721-04:00 level=INFO msg=Sensitive operation started, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:39-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1"
2025-08-06T14:27:39.748-04:00 level=INFO msg=Sensitive operation completed, path="/v1/admin/users/blZ85CFN2CF/properties", status=200, duration="26.332469ms", clientIP="::1", responseSize=4287, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET"
2025-08-06T14:27:39.748-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T14:27:52.303-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/property_detail/index.html", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=301
2025-08-06T14:27:52.308-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/property_detail/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:52.321-04:00 level=INFO msg=Admin operation, path="/pages/admin/property_detail/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET"
2025-08-06T14:27:52.331-04:00 level=INFO msg=Sensitive operation started, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:52-04:00", method="GET", path="/v1/admin/properties/b6pyHweN0PW", clientIP="::1"
2025-08-06T14:27:52.336-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/properties/b6pyHweN0PW", status=200, duration="5.235077ms", clientIP="::1", responseSize=373, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:27:52.336-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/properties/b6pyHweN0PW", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:27:54.985-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/blZ85CFN2CF", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:54-04:00"
2025-08-06T14:27:54.989-04:00 level=INFO msg=Sensitive operation completed, duration="3.720996ms", clientIP="::1", responseSize=153, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF", status=200
2025-08-06T14:27:54.989-04:00 level=INFO msg=Admin operation, clientIP="::1", status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:27:54.995-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:27:54-04:00"
2025-08-06T14:27:55.028-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=4287, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", status=200, duration="32.604546ms"
2025-08-06T14:27:55.028-04:00 level=INFO msg=Admin operation, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties"
2025-08-06T14:28:32.798-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/blZ85CFN2CF", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:28:32-04:00"
2025-08-06T14:28:32.8-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF", status=200, duration="2.302416ms", clientIP="::1", responseSize=153, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:28:32.8-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:28:32.809-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:28:32-04:00"
2025-08-06T14:28:32.819-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", status=200, duration="10.445459ms", clientIP="::1", responseSize=1262, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:28:32.819-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:28:32.821-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:28:32-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:28:32.833-04:00 level=INFO msg=Sensitive operation completed, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", status=200, duration="12.069084ms", clientIP="::1", responseSize=3505
2025-08-06T14:28:32.833-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T14:28:32.836-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:28:32-04:00", method="GET"
2025-08-06T14:28:32.858-04:00 level=INFO msg=Sensitive operation completed, path="/v1/admin/users/blZ85CFN2CF/properties", status=200, duration="22.359334ms", clientIP="::1", responseSize=1262, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET"
2025-08-06T14:28:32.858-04:00 level=INFO msg=Admin operation, clientIP="::1", status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:28:32.861-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:28:32-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:28:32.879-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", status=200, duration="17.753417ms", clientIP="::1", responseSize=3505, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:28:32.879-04:00 level=INFO msg=Admin operation, path="/v1/admin/users/blZ85CFN2CF/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET"
2025-08-06T14:28:34.931-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/lease_all/index.html", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=301
2025-08-06T14:28:34.936-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/lease_all/", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:28:34.947-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/lease_all/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:28:34.956-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:28:34-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF"
2025-08-06T14:28:34.959-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF", status=200, duration="2.419625ms", clientIP="::1", responseSize=153, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:28:34.959-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:28:34.966-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:28:34-04:00"
2025-08-06T14:28:34.984-04:00 level=INFO msg=Sensitive operation completed, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", status=200, duration="17.688084ms", clientIP="::1", responseSize=8121
2025-08-06T14:28:34.984-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:29:04.487-04:00 level=INFO msg=Admin operation, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=301, method="GET", path="/pages/admin/lease_detail/index.html"
2025-08-06T14:29:04.492-04:00 level=INFO msg=Admin operation, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET", path="/pages/admin/lease_detail/"
2025-08-06T14:29:04.506-04:00 level=INFO msg=Admin operation, method="GET", path="/pages/admin/lease_detail/main.js", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:29:04.514-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:04-04:00", method="GET", path="/v1/admin/leases/5unOeWzrnZj", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:29:04.523-04:00 level=INFO msg=Sensitive operation completed, status=200, duration="9.040649ms", clientIP="::1", responseSize=1309, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/leases/5unOeWzrnZj"
2025-08-06T14:29:04.523-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/leases/5unOeWzrnZj", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:29:04.529-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:04-04:00", method="GET", path="/v1/admin/leases/5unOeWzrnZj/payments", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:29:04.535-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=2, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/leases/5unOeWzrnZj/payments", status=200, duration="6.615748ms"
2025-08-06T14:29:04.535-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/leases/5unOeWzrnZj/payments", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T14:29:08.08-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/blZ85CFN2CF", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:08-04:00"
2025-08-06T14:29:08.082-04:00 level=INFO msg=Sensitive operation completed, responseSize=153, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF", status=200, duration="2.540441ms", clientIP="::1"
2025-08-06T14:29:08.083-04:00 level=INFO msg=Admin operation, status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1"
2025-08-06T14:29:08.093-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:08-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases"
2025-08-06T14:29:08.11-04:00 level=INFO msg=Sensitive operation completed, status=200, duration="16.836265ms", clientIP="::1", responseSize=8121, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases"
2025-08-06T14:29:08.11-04:00 level=INFO msg=Admin operation, clientIP="::1", status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:29:38.094-04:00 level=INFO msg=Sensitive operation started, method="GET", path="/v1/admin/users/blZ85CFN2CF", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:38-04:00"
2025-08-06T14:29:38.096-04:00 level=INFO msg=Sensitive operation completed, path="/v1/admin/users/blZ85CFN2CF", status=200, duration="2.56925ms", clientIP="::1", responseSize=153, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET"
2025-08-06T14:29:38.096-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:29:38.099-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:38-04:00", method="GET"
2025-08-06T14:29:38.11-04:00 level=INFO msg=Sensitive operation completed, duration="10.489999ms", clientIP="::1", responseSize=1262, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", status=200
2025-08-06T14:29:38.11-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:29:38.112-04:00 level=INFO msg=Sensitive operation started, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:38-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", clientIP="::1"
2025-08-06T14:29:38.123-04:00 level=INFO msg=Sensitive operation completed, responseSize=3505, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", status=200, duration="10.595291ms", clientIP="::1"
2025-08-06T14:29:38.123-04:00 level=INFO msg=Admin operation, path="/v1/admin/users/blZ85CFN2CF/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200, method="GET"
2025-08-06T14:29:38.125-04:00 level=INFO msg=Sensitive operation started, userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:38-04:00", method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:29:38.141-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", status=200, duration="15.778832ms", clientIP="::1", responseSize=1262, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:29:38.141-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users/blZ85CFN2CF/properties", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:29:38.144-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users/blZ85CFN2CF/leases", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:38-04:00", method="GET"
2025-08-06T14:29:38.154-04:00 level=INFO msg=Sensitive operation completed, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", status=200, duration="9.841166ms", clientIP="::1", responseSize=3505, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:29:38.154-04:00 level=INFO msg=Admin operation, clientIP="::1", status=200, method="GET", path="/v1/admin/users/blZ85CFN2CF/leases", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:29:38.704-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:38-04:00", method="GET", path="/v1/admin/users"
2025-08-06T14:29:38.709-04:00 level=INFO msg=Sensitive operation completed, status=200, duration="5.844833ms", clientIP="::1", responseSize=1312, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users"
2025-08-06T14:29:38.709-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:29:56.211-04:00 level=INFO msg=Sensitive operation started, path="/v1/admin/users", clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:56-04:00", method="GET"
2025-08-06T14:29:56.215-04:00 level=INFO msg=Sensitive operation completed, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", method="GET", path="/v1/admin/users", status=200, duration="4.937ms", clientIP="::1", responseSize=1312
2025-08-06T14:29:56.216-04:00 level=INFO msg=Admin operation, method="GET", path="/v1/admin/users", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", clientIP="::1", status=200
2025-08-06T14:29:59.865-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:29:59-04:00", method="POST", path="/v1/auth/logout"
2025-08-06T14:29:59.866-04:00 level=INFO msg=Sensitive operation completed, method="POST", path="/v1/auth/logout", status=204, duration="275.166µs", clientIP="::1", responseSize=0, userID="45aaebe3-c7c5-4275-b1ab-8e107b20b48e"
2025-08-06T14:30:31.399-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/login", clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:30:31-04:00"
2025-08-06T14:30:31.502-04:00 level=INFO msg=Sensitive operation completed, status=200, duration="102.887699ms", clientIP="::1", responseSize=163, method="POST", path="/v1/auth/login"
2025-08-06T14:30:57.193-04:00 level=INFO msg=Sensitive operation started, path="/v1/leases/UENxtYxX6UE", clientIP="::1", userID="ZbqaKSetPJE", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:30:57-04:00", method="DELETE"
2025-08-06T14:30:57.201-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST"
2025-08-06T14:30:57.202-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-06T14:30:57.203-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "../uploads/lease_documents"
]
2025-08-06T14:30:57.203-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": true,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-06T14:30:57.209-04:00 level=INFO msg=Successfully deleted lease file, leaseId="UENxtYxX6UE", filePath="100/b00b5/K7zeiH2gnKGjVFa-m1SWK.pdf"
2025-08-06T14:30:57.209-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": true,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-06T14:30:57.209-04:00 level=INFO msg=Successfully deleted lease file, leaseId="UENxtYxX6UE", filePath="100/48eb8/q0GK3b9HdyV1RFCHCZgON.pdf"
2025-08-06T14:30:57.209-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": true,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-06T14:30:57.209-04:00 level=INFO msg=Successfully deleted lease file, leaseId="UENxtYxX6UE", filePath="100/452fc/ri9k-NdEOLF8LlAbQGAq4.pdf"
2025-08-06T14:30:57.209-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": true,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-06T14:30:57.21-04:00 level=INFO msg=Successfully deleted lease file, filePath="100/069cf/oLJb2bW9Imjx5F4Azqdrb.pdf", leaseId="UENxtYxX6UE"
2025-08-06T14:30:57.216-04:00 level=INFO msg=Successfully deleted lease payments, deletedCount=1, leaseId="UENxtYxX6UE"
2025-08-06T14:30:57.224-04:00 level=INFO msg=Successfully deleted lease and all related data, fileCount=4, leaseId="UENxtYxX6UE"
2025-08-06T14:30:57.242-04:00 level=INFO msg=Sensitive operation completed, responseSize=-1, userID="ZbqaKSetPJE", method="DELETE", path="/v1/leases/UENxtYxX6UE", status=204, duration="49.263746ms", clientIP="::1"
2025-08-06T14:42:54.117-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/login", clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T14:42:54-04:00"
2025-08-06T14:42:54.211-04:00 level=INFO msg=Sensitive operation completed, clientIP="::1", responseSize=167, method="POST", path="/v1/auth/login", status=200, duration="95.056308ms"
2025-08-06T16:19:03.336-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T16:19:03.343-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T16:19:03.349-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T16:19:03.349-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T16:19:03.35-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T16:19:03.35-04:00 level=INFO msg=Created upload directory, path="../uploads/lease_documents"
2025-08-06T16:19:03.35-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/lease_docs"
2025-08-06T16:19:03.35-04:00 level=INFO msg=Created upload directory, path="../uploads/property_documents"
2025-08-06T16:19:03.35-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/property_docs"
2025-08-06T16:19:03.35-04:00 level=INFO msg=Created upload directory, path="../uploads/problem_reports"
2025-08-06T16:19:03.35-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/problem_files"
2025-08-06T16:19:03.35-04:00 level=INFO msg=Created upload directory, path="../uploads/metro2_reports"
2025-08-06T16:19:03.35-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/metro2_files"
2025-08-06T16:19:03.35-04:00 level=INFO, value="Goupload configuration validated successfully"
2025-08-06T16:19:03.35-04:00 level=INFO, value="Goupload initialized successfully"
2025-08-06T16:19:03.352-04:00 level=INFO msg=Static file serving enabled, path="build/web/dist"
2025-08-06T16:19:03.352-04:00 level=INFO msg=Server starting, port=8089
2025-08-06T16:19:03.352-04:00 level=INFO msg=Server available, url="http://localhost:8089/"
2025-08-06T16:19:50.404-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T16:19:50.411-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T16:19:50.418-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T16:19:50.418-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T16:19:50.418-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T16:19:50.419-04:00 level=INFO msg=Created upload directory, path="../uploads/lease_documents"
2025-08-06T16:19:50.419-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/lease_docs"
2025-08-06T16:19:50.419-04:00 level=INFO msg=Created upload directory, path="../uploads/property_documents"
2025-08-06T16:19:50.419-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/property_docs"
2025-08-06T16:19:50.419-04:00 level=INFO msg=Created upload directory, path="../uploads/problem_reports"
2025-08-06T16:19:50.419-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/problem_files"
2025-08-06T16:19:50.419-04:00 level=INFO msg=Created upload directory, path="../uploads/metro2_reports"
2025-08-06T16:19:50.419-04:00 level=INFO msg=Created upload directory, path="../tmp/goupload/metro2_files"
2025-08-06T16:19:50.419-04:00 level=INFO, value="Goupload configuration validated successfully"
2025-08-06T16:19:50.419-04:00 level=INFO, value="Goupload initialized successfully"
2025-08-06T16:19:50.42-04:00 level=INFO msg=Static file serving enabled, path="../build/web/dist"
2025-08-06T16:19:50.42-04:00 level=INFO msg=Server starting, port=8089
2025-08-06T16:19:50.421-04:00 level=INFO msg=Server available, url="http://localhost:8089/"
2025-08-06T16:20:45.946-04:00 level=INFO msg=Sensitive operation started, method="POST", path="/v1/auth/logout", clientIP="::1", userID="ZbqaKSetPJE", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T16:20:45-04:00"
2025-08-06T16:20:45.948-04:00 level=INFO msg=Sensitive operation completed, path="/v1/auth/logout", status=204, duration="2.452489ms", clientIP="::1", responseSize=0, userID="ZbqaKSetPJE", method="POST"
2025-08-06T16:20:47.55-04:00 level=INFO msg=Sensitive operation started, clientIP="::1", userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T16:20:47-04:00", method="GET", path="/v1/auth/check"
2025-08-06T16:20:48.685-04:00 level=INFO msg=Sensitive operation started, userID="", userAgent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", timestamp="2025-08-06T16:20:48-04:00", method="GET", path="/v1/auth/check", clientIP="::1"
2025-08-06T16:29:01.615-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-08-06T16:29:01.62-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-08-06T16:29:01.624-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-06T16:29:01.624-04:00 level=INFO msg=Registered predefined collection, collection="mailLog", database="rr", collName="mail_log"
2025-08-06T16:29:01.625-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-08-06T16:29:01.625-04:00 level=INFO msg=Processing Metro2 notification tasks, delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-08-06T16:29:01.626-04:00 level=INFO, value="Processing Metro2 notification tasks"
2025-08-06T16:29:01.628-04:00 level=INFO msg=Retrieved Metro2 notification tasks, count=0
2025-08-06T16:29:01.628-04:00 level=INFO, value="No pending Metro2 notification tasks found"
2025-08-06T16:29:01.63-04:00 level=INFO msg=Completed Metro2 notification tasks cleanup, cleanupDays=30
2025-08-06T16:29:01.631-04:00 level=INFO, value="Metro2 notification CLI completed successfully"
